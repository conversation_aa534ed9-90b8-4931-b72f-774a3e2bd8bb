# 医疗设备管理系统安装与配置文档

## 1. 系统要求

### 1.1 服务器环境要求

- **操作系统**：Windows 或 Linux
- **Web 服务器**：Apache 或 Nginx
- **PHP 版本**：PHP 5.6.40 或更高版本
- **MySQL 版本**：5.5 或更高版本
- **PHP 扩展**：
  - PDO
  - PDO_MySQL
  - OpenSSL
  - Mbstring
  - XML
  - Fileinfo
  - php_screw_plus.so（必须）

### 1.2 目录权限要求

以下目录需要可写权限：

- `Application/Common/cache`
- `Application/Runtime`

## 2. 安装步骤

### 2.1 准备工作

1. 确保服务器满足上述系统要求
2. 准备好 MySQL 数据库账号和密码
3. 下载系统安装包并解压到 Web 服务器根目录

### 2.2 安装向导

系统提供了简便的安装向导，按照以下步骤操作：

1. 在浏览器中访问：`http://您的域名/install/`
2. 安装向导将引导您完成以下步骤：
   - 阅读并接受许可协议
   - 环境检测
   - 数据库配置
   - 管理员账号设置
   - 安装完成

#### 2.2.1 许可协议

阅读系统许可协议，勾选"我已阅读并同意许可协议"，点击"下一步"。

#### 2.2.2 环境检测

系统会自动检测服务器环境是否满足要求，包括：

- PHP 版本检测
- 必要 PHP 扩展检测
- 目录权限检测

如有不符合要求的项目，请按提示进行修复后再继续。

#### 2.2.3 数据库配置

填写数据库连接信息：

- 数据库主机：默认为 127.0.0.1
- 端口：默认为 3306
- 数据库名：您的数据库名称
- 用户名：数据库用户名
- 密码：数据库密码

点击"下一步"，系统将自动创建数据库并导入基础数据。

#### 2.2.4 管理员配置

设置系统管理员账号信息：

- 用户名：管理员登录用户名
- 密码：管理员登录密码
- 确认密码：再次输入密码
- 邮箱：管理员联系邮箱

#### 2.2.5 安装完成

安装完成后，系统会创建安装锁定文件`config/installed.lock`，防止重复安装。

### 2.3 安装后配置

安装完成后，您需要进行以下配置：

1. 删除`install`目录，以提高系统安全性
2. 访问系统后台：`http://您的域名/A/`，使用安装时设置的管理员账号登录

### 2.4 php_screw_plus.so 扩展安装

系统必须安装 php_screw_plus.so 扩展，请按照以下步骤操作：

#### 2.4.1 Linux 环境安装

1. 下载扩展文件 php_screw_plus.so
2. 将文件复制到 PHP 扩展目录 (通常为 /usr/lib/php/modules/ 或 /usr/lib64/php/modules/)
3. 在 php.ini 中添加以下行：
   ```
   extension=php_screw_plus.so
   ```
4. 重启 Web 服务器：
   ```bash
   # Apache
   systemctl restart httpd
   # 或 Nginx
   systemctl restart php-fpm
   ```

#### 2.4.2 Windows 环境安装

1. 下载扩展文件 php_screw_plus.dll
2. 将文件复制到 PHP 扩展目录 (通常为 PHP 安装目录/ext/)
3. 在 php.ini 中添加以下行：
   ```
   extension=php_screw_plus
   ```
4. 重启 Web 服务器

#### 2.4.3 验证安装

安装完成后，可通过以下方法验证扩展是否已正确加载：

1. 创建 phpinfo.php 文件，内容为 `<?php phpinfo(); ?>`
2. 在浏览器中访问此文件
3. 搜索 "screw_plus"，确认扩展已加载

## 3. 系统配置

### 3.1 基础配置

登录系统后台，进入"系统设置"→"系统参数"，配置以下基本信息：

- **系统标题**：设置系统显示的名称
- **公司名称**：设置公司版权名称
- **公司简称**：设置公司简称
- **系统 Logo**：上传系统 Logo 图片

### 3.2 模块配置

系统各模块可以单独配置开启或关闭，进入"系统设置"→"模块设置"：

- 设备管理模块
- 维修管理模块
- 巡查保养模块
- 质控管理模块
- 采购管理模块
- 微信端/飞书端

### 3.3 微信公众号配置

如需使用微信公众号功能，需进行以下配置：

1. 进入"系统设置"→"微信设置"
2. 填写微信公众号的 AppID 和 AppSecret
3. 设置微信公众号的服务器配置和授权回调域名

### 3.4 飞书配置（可选）

如需使用飞书功能，需进行以下配置：

1. 进入"系统设置"→"飞书设置"
2. 将"使用微信 or 飞书"设置为"1"（飞书）
3. 填写飞书应用的相关配置信息

### 3.5 部门与用户配置

1. 进入"系统管理"→"部门管理"，设置医院部门结构
2. 进入"系统管理"→"用户管理"，添加系统用户并分配权限
3. 进入"系统管理"→"角色管理"，设置用户角色和权限

### 3.6 设备分类配置

进入"设备管理"→"设备分类"，设置医疗设备分类体系。

## 4. 数据初始化

### 4.1 基础数据导入

系统安装时已自动导入基础数据，如需导入测试数据或其他数据，可使用以下方法：

1. 使用 MySQL 客户端工具连接数据库
2. 导入`tecev_item_test_data_20250427.sql`文件（如有）

### 4.2 数据备份

建议在正式使用前进行数据备份：

1. 进入"系统管理"→"数据备份"
2. 点击"立即备份"，系统会自动生成备份文件

## 5. 多端配置

### 5.1 PC 端配置

PC 端为系统默认端，访问地址：`http://您的域名/A/`

### 5.2 移动端配置

移动端访问地址：`http://您的域名/M/`

### 5.3 微信端配置

微信端访问地址：`http://您的域名/P/`

需在微信公众平台配置以下信息：

- 服务器地址：`http://您的域名/P/Login/Index/getUserOpenId`
- 消息加解密方式：明文模式
- 授权回调页面域名：您的域名

### 5.4 飞书端配置（可选）

飞书端访问地址：`http://您的域名/F/`

## 6. 定时任务配置

### 6.1 Linux 环境

在 Linux 环境下，使用 crontab 配置定时任务：

```bash
# 编辑crontab
crontab -e

# 添加定时任务，每分钟执行一次
* * * * * php /path/to/your/project/ThinkPHP/Library/Think/Crontab.php
```

### 6.2 Windows 环境

在 Windows 环境下，使用计划任务配置：

1. 打开控制面板 → 系统和安全 → 管理工具 → 计划任务
2. 创建新任务，设置触发器为每分钟执行一次
3. 操作选择执行脚本，如`redis.vbs`等

## 7. 常见问题

### 7.1 安装问题

**问题**：安装时提示"系统已经安装"  
**解决方法**：删除`config/installed.lock`文件后重新安装

**问题**：安装时环境检测不通过  
**解决方法**：按照提示安装所需 PHP 扩展或设置目录权限

**问题**：无法加载 php_screw_plus 扩展  
**解决方法**：

- 确认扩展文件已复制到正确的目录
- 确认 php.ini 中的配置路径正确
- 检查扩展文件权限
- 查看 Web 服务器和 PHP 错误日志以获取详细错误信息

### 7.2 登录问题

**问题**：管理员密码忘记  
**解决方法**：通过 MySQL 客户端修改`sb_user`表中管理员的密码字段

**问题**：微信端无法登录  
**解决方法**：检查微信公众号配置是否正确，确认微信端是否已开启

### 7.3 数据库问题

**问题**：数据库连接失败  
**解决方法**：检查`config/database.php`文件中的数据库配置是否正确

## 8. 安全建议

1. 安装完成后删除`install`目录
2. 定期修改管理员密码
3. 设置合理的用户权限
4. 定期备份数据库
5. 服务器配置防火墙，只开放必要端口
6. 使用 HTTPS 协议加密传输数据

## 9. 技术支持

- 官方网站：www.tecev.com
- 技术支持邮箱：[待补充]
- 技术支持电话：[待补充]
