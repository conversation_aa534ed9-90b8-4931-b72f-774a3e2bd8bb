<?php
$roleMenus = array(
    '0' => array(
        'name' => '科室护士',
        'role' => array(
            'Assets' => array(
                'Lookup'=>array(
                    'getAssetsList',//设备查询列表
                    'getAssetsSearchList',//设备综合查询列表
                    'assetsLifeList',//生命历程列表
                ),
                'Transfer'=>array(
                    'getList',//转科申请列表
                    'add',//转科
                    'batchAdd',//批量转移科室
                    'progress',//转科进程
                    'checkLists',//转科验收列表
                    'check',//验收
                ),
                'Scrap' => array(
                    'getScrapList',//报废查询列表
                    'getApplyList',//报废申请列表
                    'getResultList',//报废结果列表
                    'applyScrap',//申请报废
                ),
                'Borrow'=>array(
                    'borrowAssetsList',//借调申请设备列表
                    'applyBorrow',//借调申请操作
                    'borrowLife',//借调进程
                    'borrowInCheckList',//借入验收列表
                    'giveBackCheckList',//归还验收列表
                    'borrowInCheck',//借入验收
                    'giveBackCheck',//归还验收
                    'borrowRecordList',//借调记录查询
                ),
                'Outside'=>array(
                    'getDepartAssetsList',//外调申请设备列表
                    'applyAssetOutSide',//外调申请操作
                    'outSideResultList',//外调结果列表
                    'checkOutSiteAsset',//外调验收单录入
                    'showOutSideResult',//外调结果明细查看
                ),
                'Subsidiary'=>array(
                    'subsidiaryAllotList',//分配申请列表
                    'subsidiaryAllot',//分配申请操作
                    'subsidiaryCheckList',//分配验收列表
                    'subsidiaryCheck',//分配验收操作
                ),
            ),
            'Repair'=>array(
                'Repair'=>array(
                    'getAssetsLists',//设备报修列表
                    'addRepair',//报修
                    'examine',//科室验收
                    'checkRepair',//验收
                    'progress',//维修进程列表
                ),
                'RepairSearch'=>array(
                    'getRepairSearchList',//维修记录列表
                ),
            ),
            'Patrol'=>array(
                'Patrol'=>array(
                    'examineList',//科室验收列表
                    'examine',//巡查验收
                ),
                'PatrolRecords'=>array(
                    'getPatrolRecords',//设备保养记录查询
                    'printReports',//批量打印保养报告
                    'exportReports',//批量导出
                )
            ),
            'Adverse'=>array(
                'Adverse'=>array(
                    'getAdverseList',//器械不良事件报告列表
                    'addAdverse',//新增不良事件报告
                    'editAdverse',//编辑不良事件报告
                )
            ),
            'Archives' => array(
                'Emergency'=>array(
                    'emergencyPlanList',//应急预案列表
                )
            )
        )
    ),
    '1' => array(
        'name' => '科室工程师',
        'role' => array(
            'Assets' => array(
                'Lookup'=>array(
                    'getAssetsList',//设备查询列表
                    'getAssetsSearchList',//设备综合查询列表
                    'assetsLifeList',//生命历程列表
                )
            ),
            'Repair' => array(
                'Repair' => array(
                    'ordersLists',//接单列表
                    'getRepairLists',//维修处理列表
                    'accept',//接单
                    'startRepair',//开始维修
                    'uploadRepair',//上传维修文件
                    'progress',//维修进程列表
                    'overhaul',//检修
                    'batchAddRepair',//集中录入
                ),
                'RepairSearch'=>array(
                    'getRepairSearchList',//维修记录列表
                ),
                'Statistics'=>array(
                    'faultSummary',//设备故障统计
                    'exportFaultSummary',//导出故维修障统计表格
                    'exportFaultDetailed',//导出设备故障明细表格
                )
            ),
            'Patrol'=>array(
                'Patrol'=>array(
                    'tasksList',//任务实施列表
                    'doTask',//执行任务
                ),
                'PatrolRecords'=>array(
                    'getPatrolRecords',//设备保养记录查询
                    'printReports',//批量打印保养报告
                    'exportReports',//批量导出
                )
            ),
            'Qualities'=>array(
                'Quality'=>array(
                    'showQualityPlan',//质控计划详情
                    'qualityDetailList',//明细录入列表
                    'setQualityDetail',//明细录入/执行计划
                    'editQualityDetail',//修改明细
                    'qualityResult',//质控结果查询
                ),
                'QualityStatistics'=>array(
                    'qualityDepartStatistics',//科室质控报表
                    'exportDepartStatistics',//导出报表
                )
            ),
            'Metering'=>array(
                'Metering'=>array(
                    'getMeteringResult',//计量检定结果
                    'setMeteringResult',//计量检测
                )
            ),
            'Archives' => array(
                'Emergency'=>array(
                    'emergencyPlanList',//应急预案列表
                    'addEmergency',//添加应急预案
                    'editEmergency',//修改应急预案
                    'delEmergency',//删除应急预案
                )
            )
        )
    ),
    '2' => array(
        'name' => '科室负责人',
        'role' => array(
            'Assets' => array(
                'Scrap' => array(
                    'getApplyList',//报废申请列表
                    'applyScrap',//报废申请
                    'getExamineList',//报废审核列表
                    'examine',//报废审核
                    'getScrapList',//报废查询
                    'getResultList',//报废结果
                    'result',//报废处置
                ),
                'Lookup' => array(
                    'getAssetsList',//设备查询列表
                    'showAssetsPrice',//显示设备原值
                    'assetsLifeList',//设备生命历程
                    'getInsuranceList',//设备参保列表
                    'doRenewal',//续保
                ),
                'Transfer' => array(
                    'getList',//转科申请
                    'add',//转科
                    'batchAdd',//批量转移科室
                    'progress',//转科进程
                    'examine',//转科审批
                    'approval',//审批
                    'checkLists',//转科验收
                    'check',//验收
                ),
                'Statistics' => array(
                    'onUseAssetsSurvey',//在用设备统计
                    'assetsSummary',//设备汇总统计
                    'assetsSurvey',//设备概况
                    'exportAssetsSurvey',//导出设备概况
                    'exportUsingAssetsSurvey',//导出在用设备概况
                    'exportAssetsSummary',//导出设备汇总统计概况
                    'exportDepartmentSummary',//导出部门电子账单
                    'exportRepairRecord',//导出设备维修记录
                    'MaintenanceSummary',//参保信息统计
                ),
                'Borrow' => array(
                    'borrowAssetsList',//借调申请列表
                    'applyBorrow',//申请
                    'borrowLife',//借调进程
                    'approveBorrowList',//借调审批列表
                    'departApproveBorrow',//借调部门审批
                    'borrowInCheckList',//借入验收列表
                    'borrowInCheck',//借入验收
                    'giveBackCheckList',//归还验收列表
                    'giveBackCheck',//归还验收
                    'borrowRecordList',//归还验收
                ),
                'Outside' => array(
                    'getDepartAssetsList',//外调申请列表
                    'applyAssetOutSide',//申请
                    'outSideApproveList',//外调审批列表
                    'assetOutSideApprove',//审批
                    'outSideResultList',//外调结果
                    'checkOutSiteAsset',//验收单录入
                ),
                'Subsidiary' => array(
                    'subsidiaryAllotList',//分配申请列表
                    'subsidiaryAllot',//申请
                    'subsidiaryApproveList',//分配审批列表
                    'subsidiaryApprove',//审批
                    'subsidiaryCheckList',//分配验收列表
                    'subsidiaryCheck',//验收
                ),
                'Print' => array(
                    'printAssets',//标签打印
                )
            ),
            'Repair' => array(
                'Repair' => array(
                    'ordersLists',//接单列表
                    'getRepairLists',//维修处理列表
                    'accept',//接单
                    'startRepair',//开始维修
                    'uploadRepair',//上传维修文件
                    'progress',//维修进程列表
                    'overhaul',//检修
                    'batchAddRepair',//集中录入
                    'dispatchingLists',//派工响应列表
                    'assigned',//指派
                    'getAssetsLists',//设备报修列表
                    'addRepair',//报修
                    'repairApproveLists',//维修审批列表
                    'addApprove',//审批
                    'examine',//验收列表
                    'checkRepair',//验收
                ),
                'RepairSearch'=>array(
                    'getRepairSearchList',//维修记录列表
                ),
                'Statistics'=>array(
                    'faultSummary',//设备故障统计
                    'exportFaultSummary',//导出故维修障统计表格
                    'exportFaultDetailed',//导出设备故障明细表格
                )
            ),
            'Patrol'=>array(
                'Patrol'=>array(
                    'tasksList',//任务实施列表
                    'doTask',//执行任务
                    'patrolList',//计划查询列表
                    'addPatrol',//新增巡查保养
                    'editPatrol',//修订计划
                    'releasePatrol',//发布计划
                    'examineList',//科室验收列表
                    'examine',//巡查验收
                    'patrolApprove',//巡查计划审核
                    'approve',//审核
                ),
                'PatrolRecords'=>array(
                    'getPatrolRecords',//设备保养记录查询
                    'printReports',//批量打印保养报告
                    'exportReports',//批量导出
                ),
                'Statistics'=>array(
                    'patrolPlanSurvey',//巡查计划概况
                    'exportPlanSummary',//导出巡查计划概况
                    'exportPlanLists',//导出设备计划记录
                ),
                'PatrolSetting'=>array(
                    'template',//模板维护
                    'initialization',//设备初始化模板
                    'batchSettingTemplate',//设定模板
                    'addTemplate',//新增模板
                )
            ),
            'Qualities'=>array(
                'Quality'=>array(
                    'getQualityList',//质控计划制定
                    'addQuality',//新增质控计划
                    'startQualityPlan',//启用质控计划
                    'editQualityPlan',//编辑质控计划
                    'feedbackQuality',//接收每日质控反馈
                    'showQualityPlan',//质控计划详情
                    'qualityDetailList',//明细录入列表
                    'setQualityDetail',//明细录入/执行计划
                    'editQualityDetail',//修改明细
                    'qualityResult',//质控结果查询
                ),
                'QualityStatistics'=>array(
                    'qualityDepartStatistics',//科室质控报表
                    'exportDepartStatistics',//导出报表
                )
            ),
            'Adverse'=>array(
                'Adverse'=>array(
                    'getAdverseList',//器械不良事件报告列表
                    'addAdverse',//新增不良事件报告
                    'editAdverse',//编辑不良事件报告
                )
            ),
            'Benefit' => array(
                'Benefit' => array(
                    'assetsBenefitList',//设备收支录入
                    'singleBenefitList',//单机数据分析
                    'departmentBenefitList',//科室数据分析
                    'batchAddBenefit',//批量录入收支明细
                    'exportBenefit',//批量导出收支明细
                )
            ),
            'Metering' => array(
                'Metering' => array(
                    'getMeteringList',//计量计划制定
                    'addMetering',//新增计量计划
                    'batchAddMetering',//批量新增计量计划
                    'saveMetering',//编辑
                    'delMetering',//删除
                    'getMeteringResult',//计量检定结果
                    'setMeteringResult',//检测
                )
            ),
            'Statistics' => array(
                'AdverseStatis' => array(
                    'adverseAnalysis',//不良事件报表分析
                ),
                'QualityStatis' => array(
                    'qualityAnalysis',//质控计划报表分析
                    'resultAnalysis',//质控结果报表分析
                ),
                'RepairStatis' => array(
                    'repairFeeStatis',//维修费用统计
                    'repairAnalysis',//维修费用分析
                    'engineerCompar',//工程师工作量对比
                    'engineerEva',//工程师评价对比
                    'repairFeeTrend',//维修设备趋势分析
                ),
                'PurchasesStatis' => array(
                    'purFeeStatis',//采购费用统计
                    'purAnalysis',//采购费用分析
                )
            ),
            'Archives' => array(
                'Emergency'=>array(
                    'emergencyPlanList',//应急预案列表
                    'addEmergency',//添加应急预案
                    'editEmergency',//修改应急预案
                    'delEmergency',//删除应急预案
                )
            )
        ),
    ),
    '3' => array(
        'name' => '设备科负责人',
        'role' => array(
            'Assets' => array(
                'Scrap' => array(
                    'getApplyList',//报废申请列表
                    'applyScrap',//报废申请
                    'getExamineList',//报废审核列表
                    'examine',//报废审核
                    'getScrapList',//报废查询
                    'getResultList',//报废结果
                    'result',//报废处置
                ),
                'Lookup' => array(
                    'getAssetsList',//设备查询列表
                    'showAssetsPrice',//显示设备原值
                    'assetsLifeList',//设备生命历程
                    'getInsuranceList',//设备参保列表
                    'doRenewal',//续保
                    'batchAddAssets',//批量添加主设备
                    'addAssets',//添加主设备
                    'editAssets',//修改主设备
                    'deleteAssets',//删除主设备
                    'batchEditAssets',//批量修改主设备
                    'exportAssets',//导出主设备
                ),
                'Transfer' => array(
                    'getList',//转科申请
                    'add',//转科
                    'batchAdd',//批量转移科室
                    'progress',//转科进程
                    'examine',//转科审批
                    'approval',//审批
                    'checkLists',//转科验收
                    'check',//验收
                ),
                'Statistics' => array(
                    'onUseAssetsSurvey',//在用设备统计
                    'assetsSummary',//设备汇总统计
                    'assetsSurvey',//设备概况
                    'exportAssetsSurvey',//导出设备概况
                    'exportUsingAssetsSurvey',//导出在用设备概况
                    'exportAssetsSummary',//导出设备汇总统计概况
                    'exportDepartmentSummary',//导出部门电子账单
                    'exportRepairRecord',//导出设备维修记录
                    'MaintenanceSummary',//参保信息统计
                ),
                'Borrow' => array(
                    'borrowAssetsList',//借调申请列表
                    'applyBorrow',//申请
                    'borrowLife',//借调进程
                    'approveBorrowList',//借调审批列表
                    'departApproveBorrow',//借调部门审批
                    'assetsApproveBorrow',//设备科审批
                    'borrowInCheckList',//借入验收列表
                    'borrowInCheck',//借入验收
                    'giveBackCheckList',//归还验收列表
                    'giveBackCheck',//归还验收
                    'borrowRecordList',//归还验收
                ),
                'Outside' => array(
                    'getDepartAssetsList',//外调申请列表
                    'applyAssetOutSide',//申请
                    'outSideApproveList',//外调审批列表
                    'assetOutSideApprove',//审批
                    'outSideResultList',//外调结果
                    'checkOutSiteAsset',//验收单录入
                ),
                'Subsidiary' => array(
                    'subsidiaryAllotList',//分配申请列表
                    'subsidiaryAllot',//申请
                    'subsidiaryApproveList',//分配审批列表
                    'subsidiaryApprove',//审批
                    'subsidiaryCheckList',//分配验收列表
                    'subsidiaryCheck',//验收
                ),
                'Print' => array(
                    'design',//标签设计
                    'printAssets',//标签打印
                )
            ),
            'Repair' => array(
                'Repair' => array(
                    'ordersLists',//接单列表
                    'getRepairLists',//维修处理列表
                    'accept',//接单
                    'startRepair',//开始维修
                    'uploadRepair',//上传维修文件
                    'progress',//维修进程列表
                    'overhaul',//检修
                    'batchAddRepair',//集中录入
                    'dispatchingLists',//派工响应列表
                    'assigned',//指派
                    'getAssetsLists',//设备报修列表
                    'addRepair',//报修
                    'repairApproveLists',//维修审批列表
                    'addApprove',//审批
                    'examine',//验收列表
                    'checkRepair',//验收
                ),
                'RepairSearch'=>array(
                    'getRepairSearchList',//维修记录列表
                ),
                'Statistics'=>array(
                    'faultSummary',//设备故障统计
                    'exportFaultSummary',//导出故维修障统计表格
                    'exportFaultDetailed',//导出设备故障明细表格
                ),
                'RepairSetting'=>array(
                    'typeSetting',//故障类型设置
                    'addType',//添加故障类型
                    'editProblem',//修改故障问题
                    'deleteProblem',//删除故障问题
                    'repairAssign',//维修自动派工
                ),
                'RepairParts'=>array(
                    'partsInWareList',//配件入库列表
                    'partsInWare',//配件入库
                    'partsOutWareList',//配件出库列表
                    'partsOutWare',//配件库存
                    'partStockList',//配件库存列表
                )
            ),
            'Patrol'=>array(
                'Patrol'=>array(
                    'tasksList',//任务实施列表
                    'doTask',//执行任务
                    'patrolList',//计划查询列表
                    'addPatrol',//新增巡查保养
                    'editPatrol',//修订计划
                    'releasePatrol',//发布计划
                    'examineList',//科室验收列表
                    'examine',//巡查验收
                    'patrolApprove',//巡查计划审核
                    'approve',//审核
                ),
                'PatrolRecords'=>array(
                    'getPatrolRecords',//设备保养记录查询
                    'printReports',//批量打印保养报告
                    'exportReports',//批量导出
                ),
                'Statistics'=>array(
                    'patrolPlanSurvey',//巡查计划概况
                    'exportPlanSummary',//导出巡查计划概况
                    'exportPlanLists',//导出设备计划记录
                ),
                'PatrolSetting'=>array(
                    'template',//模板维护
                    'initialization',//设备初始化模板
                    'batchSettingTemplate',//设定模板
                    'addTemplate',//新增模板
                    'points',//保养项目类别&明细
                    'addPoints',//新增类别
                    'editDetail',//修改明细
                    'deleteDetail',//删除明细
                    'deleteTemplate',//删除模板
                    'editTemplate',//编辑模板
                )
            ),
            'Qualities'=>array(
                'Quality'=>array(
                    'getQualityList',//质控计划制定
                    'addQuality',//新增质控计划
                    'startQualityPlan',//启用质控计划
                    'editQualityPlan',//编辑质控计划
                    'feedbackQuality',//接收每日质控反馈
                    'showQualityPlan',//质控计划详情
                    'qualityDetailList',//明细录入列表
                    'setQualityDetail',//明细录入/执行计划
                    'qualityResult',//质控结果查询
                    'getDetectingList',//检测仪器管理
                    'presetQualityItem',//设备质控项目预设
                    'addPresetQI',//质控项目设置
                    'editQualityDetail',//修改明细
                    'Statistics',//质控项目设置
                ),
                'QualityStatistics'=>array(
                    'qualityDepartStatistics',//科室质控报表
                    'exportDepartStatistics',//导出报表
                )
            ),
            'Adverse'=>array(
                'Adverse'=>array(
                    'getAdverseList',//器械不良事件报告列表
                    'addAdverse',//新增不良事件报告
                    'editAdverse',//编辑不良事件报告
                )
            ),
            'Benefit' => array(
                'Benefit' => array(
                    'assetsBenefitList',//设备收支录入
                    'singleBenefitList',//单机数据分析
                    'departmentBenefitList',//科室数据分析
                    'batchAddBenefit',//批量录入收支明细
                    'exportBenefit',//批量导出收支明细
                )
            ),
            'Metering' => array(
                'Metering' => array(
                    'getMeteringList',//计量计划制定
                    'addMetering',//新增计量计划
                    'batchAddMetering',//批量新增计量计划
                    'saveMetering',//编辑
                    'delMetering',//删除
                    'getMeteringResult',//计量检定结果
                    'setMeteringResult',//检测
                )
            ),
            'Statistics' => array(
                'AdverseStatis' => array(
                    'adverseAnalysis',//不良事件报表分析
                ),
                'QualityStatis' => array(
                    'qualityAnalysis',//质控计划报表分析
                    'resultAnalysis',//质控结果报表分析
                ),
                'RepairStatis' => array(
                    'repairFeeStatis',//维修费用统计
                    'repairAnalysis',//维修费用分析
                    'engineerCompar',//工程师工作量对比
                    'engineerEva',//工程师评价对比
                    'repairFeeTrend',//维修设备趋势分析
                ),
                'PurchasesStatis' => array(
                    'purFeeStatis',//采购费用统计
                    'purAnalysis',//采购费用分析
                )
            ),
            'OfflineSuppliers' => array(
                'OfflineSuppliers' => array(
                    'offlineSuppliersList',//厂商列表
                    'addOfflineSupplier',//新增厂商
                    'editOfflineSupplier',//维护厂商信息
                    'olsContract',//合同管理
                    'payOLSContractList',//合同付款管理
                    'addOLSContract',//新增合同
                    'confirmOLSContract',//确认合同
                    'payOLSContract',//合同付款
                    'delSupplier',//删除厂商
                )
            ),
            'BaseSetting'=>array(
                'IntegratedSetting'=>array(
                    'department',//科室设置
                    'category',//主设备分类设置
                    'system',//微信参数设置
                    'operationLog',//用户行为日志
                    'addCategory',//添加主设备分类
                    'editCategory',//修改分类
                    'deleteCategory',//删除分类
                    'batchAddCategory',//批量添加分类
                    'addDepartment',//添加科室
                    'editDepartment',//修改科室
                    'deleteDepartment',//删除科室
                    'batchAddDepartment',//批量添加科室
                ),
                'Privilege'=>array(
                    'getRoleList',//角色管理
                    'editRoleUser',//成员维护
                    'addRole',//添加角色
                    'editRolePrivi',//权限维护
                    'editRole',//编辑角色
                    'deleteRole',//删除角色
                ),
                'User'=>array(
                    'getUserList',//用户管理
                    'addUser',//新增用户
                    'editUser',//修改用户
                    'deleteUser',//删除用户
                    'clearOpenid',//微信解绑
                    'batchDeleteUser',//批量删除用户
                    'batchAddUser',//批量添加用户
                    'uploadautograph',//上传用户签名
                ),
                'ApproveSetting'=>array(
                    'editProcess',//修改流程
                    'deleteProcess',//删除流程
                    'addProcess',//添加流程
                    'approveLists',//多级审批设置
                ),
                'Notice'=>array(
                    'addNotice',//发布公告
                    'editNotice',//编辑公告
                    'deleteNotice',//删除公告
                    'getNoticeList',//公告管理
                ),
                'Dictionary'=>array(
                    'assetsDic',//设备字典
                    'addAssetsDic',//新增设备字典
                    'editAssetsDic',//修改设备字典
                    'delAssetsDic',//删除设备字典
                    'partsDic',//配件字典
                    'addPartsDic',//新增配件字典
                    'editPartsDic',//修改配件字典
                    'delPartsDic',//删除配件字典
                    'brandDic',//品牌字典
                    'addBrandDic',//新增品牌
                    'editBrandDic',//修改品牌
                    'delBrandDic',//删除品牌
                    'batchAddAssetsDic',//批量添加设备字典
                ),
                'SmsModule'=>array(
                    'smsSetting',//短信配置
                ),
                'ExamApp'=>array(
                    'getExamLists',//审查批准
                    'passno',//同意/驳回
                )
            ),
            'Archives' => array(
                'Emergency'=>array(
                    'emergencyPlanList',//应急预案列表
                    'addEmergency',//添加应急预案
                    'editEmergency',//修改应急预案
                    'delEmergency',//删除应急预案
                )
            )
        ),
    ),
    '4' => array(
        'name' => '副院长&院长',
        'role' => array(
            'Assets' => array(
                'Scrap' => array(
                    'getApplyList',//报废申请列表
                    'applyScrap',//报废申请
                    'getExamineList',//报废审核列表
                    'examine',//报废审核
                    'getScrapList',//报废查询
                    'getResultList',//报废结果
                    'result',//报废处置
                ),
                'Lookup' => array(
                    'getAssetsList',//设备查询列表
                    'showAssetsPrice',//显示设备原值
                    'assetsLifeList',//设备生命历程
                    'getInsuranceList',//设备参保列表
                    'doRenewal',//续保
                    'batchAddAssets',//批量添加主设备
                    'addAssets',//添加主设备
                    'editAssets',//修改主设备
                    'deleteAssets',//删除主设备
                    'batchEditAssets',//批量修改主设备
                    'exportAssets',//导出主设备
                ),
                'Transfer' => array(
                    'getList',//转科申请
                    'add',//转科
                    'batchAdd',//批量转移科室
                    'progress',//转科进程
                    'examine',//转科审批
                    'approval',//审批
                    'checkLists',//转科验收
                    'check',//验收
                ),
                'Statistics' => array(
                    'onUseAssetsSurvey',//在用设备统计
                    'assetsSummary',//设备汇总统计
                    'assetsSurvey',//设备概况
                    'exportAssetsSurvey',//导出设备概况
                    'exportUsingAssetsSurvey',//导出在用设备概况
                    'exportAssetsSummary',//导出设备汇总统计概况
                    'exportDepartmentSummary',//导出部门电子账单
                    'exportRepairRecord',//导出设备维修记录
                    'MaintenanceSummary',//参保信息统计
                ),
                'Borrow' => array(
                    'borrowAssetsList',//借调申请列表
                    'applyBorrow',//申请
                    'borrowLife',//借调进程
                    'approveBorrowList',//借调审批列表
                    'departApproveBorrow',//借调部门审批
                    'assetsApproveBorrow',//设备科审批
                    'borrowInCheckList',//借入验收列表
                    'borrowInCheck',//借入验收
                    'giveBackCheckList',//归还验收列表
                    'giveBackCheck',//归还验收
                    'borrowRecordList',//归还验收
                ),
                'Outside' => array(
                    'getDepartAssetsList',//外调申请列表
                    'applyAssetOutSide',//申请
                    'outSideApproveList',//外调审批列表
                    'assetOutSideApprove',//审批
                    'outSideResultList',//外调结果
                    'checkOutSiteAsset',//验收单录入
                ),
                'Subsidiary' => array(
                    'subsidiaryAllotList',//分配申请列表
                    'subsidiaryAllot',//申请
                    'subsidiaryApproveList',//分配审批列表
                    'subsidiaryApprove',//审批
                    'subsidiaryCheckList',//分配验收列表
                    'subsidiaryCheck',//验收
                ),
                'Print' => array(
                    'design',//标签设计
                    'printAssets',//标签打印
                )
            ),
            'Repair' => array(
                'Repair' => array(
                    'ordersLists',//接单列表
                    'getRepairLists',//维修处理列表
                    'accept',//接单
                    'startRepair',//开始维修
                    'uploadRepair',//上传维修文件
                    'progress',//维修进程列表
                    'overhaul',//检修
                    'batchAddRepair',//集中录入
                    'dispatchingLists',//派工响应列表
                    'assigned',//指派
                    'getAssetsLists',//设备报修列表
                    'addRepair',//报修
                    'repairApproveLists',//维修审批列表
                    'addApprove',//审批
                    'examine',//验收列表
                    'checkRepair',//验收
                ),
                'RepairSearch'=>array(
                    'getRepairSearchList',//维修记录列表
                ),
                'Statistics'=>array(
                    'faultSummary',//设备故障统计
                    'exportFaultSummary',//导出故维修障统计表格
                    'exportFaultDetailed',//导出设备故障明细表格
                ),
                'RepairSetting'=>array(
                    'typeSetting',//故障类型设置
                    'addType',//添加故障类型
                    'editProblem',//修改故障问题
                    'deleteProblem',//删除故障问题
                    'repairAssign',//维修自动派工
                ),
                'RepairParts'=>array(
                    'partsInWareList',//配件入库列表
                    'partsInWare',//配件入库
                    'partsOutWareList',//配件出库列表
                    'partsOutWare',//配件库存
                    'partStockList',//配件库存列表
                )
            ),
            'Patrol'=>array(
                'Patrol'=>array(
                    'tasksList',//任务实施列表
                    'doTask',//执行任务
                    'patrolList',//计划查询列表
                    'addPatrol',//新增巡查保养
                    'editPatrol',//修订计划
                    'releasePatrol',//发布计划
                    'examineList',//科室验收列表
                    'examine',//巡查验收
                    'patrolApprove',//巡查计划审核
                    'approve',//审核
                ),
                'PatrolRecords'=>array(
                    'getPatrolRecords',//设备保养记录查询
                    'printReports',//批量打印保养报告
                    'exportReports',//批量导出
                ),
                'Statistics'=>array(
                    'patrolPlanSurvey',//巡查计划概况
                    'exportPlanSummary',//导出巡查计划概况
                    'exportPlanLists',//导出设备计划记录
                ),
                'PatrolSetting'=>array(
                    'template',//模板维护
                    'initialization',//设备初始化模板
                    'batchSettingTemplate',//设定模板
                    'addTemplate',//新增模板
                    'points',//保养项目类别&明细
                    'addPoints',//新增类别
                    'editDetail',//修改明细
                    'deleteDetail',//删除明细
                    'deleteTemplate',//删除模板
                    'editTemplate',//编辑模板
                )
            ),
            'Qualities'=>array(
                'Quality'=>array(
                    'getQualityList',//质控计划制定
                    'addQuality',//新增质控计划
                    'startQualityPlan',//启用质控计划
                    'editQualityPlan',//编辑质控计划
                    'feedbackQuality',//接收每日质控反馈
                    'showQualityPlan',//质控计划详情
                    'qualityDetailList',//明细录入列表
                    'setQualityDetail',//明细录入/执行计划
                    'qualityResult',//质控结果查询
                    'getDetectingList',//检测仪器管理
                    'presetQualityItem',//设备质控项目预设
                    'addPresetQI',//质控项目设置
                    'editQualityDetail',//修改明细
                    'Statistics',//质控项目设置
                    'delQualityPlan',//删除质控计划
                ),
                'QualityStatistics'=>array(
                    'qualityDepartStatistics',//科室质控报表
                    'exportDepartStatistics',//导出报表
                )
            ),
            'Adverse'=>array(
                'Adverse'=>array(
                    'getAdverseList',//器械不良事件报告列表
                    'addAdverse',//新增不良事件报告
                    'editAdverse',//编辑不良事件报告
                )
            ),
            'Benefit' => array(
                'Benefit' => array(
                    'assetsBenefitList',//设备收支录入
                    'singleBenefitList',//单机数据分析
                    'departmentBenefitList',//科室数据分析
                    'batchAddBenefit',//批量录入收支明细
                    'exportBenefit',//批量导出收支明细
                )
            ),
            'Metering' => array(
                'Metering' => array(
                    'getMeteringList',//计量计划制定
                    'addMetering',//新增计量计划
                    'batchAddMetering',//批量新增计量计划
                    'saveMetering',//编辑
                    'delMetering',//删除
                    'getMeteringResult',//计量检定结果
                    'setMeteringResult',//检测
                )
            ),
            'Statistics' => array(
                'AdverseStatis' => array(
                    'adverseAnalysis',//不良事件报表分析
                ),
                'QualityStatis' => array(
                    'qualityAnalysis',//质控计划报表分析
                    'resultAnalysis',//质控结果报表分析
                ),
                'RepairStatis' => array(
                    'repairFeeStatis',//维修费用统计
                    'repairAnalysis',//维修费用分析
                    'engineerCompar',//工程师工作量对比
                    'engineerEva',//工程师评价对比
                    'repairFeeTrend',//维修设备趋势分析
                ),
                'PurchasesStatis' => array(
                    'purFeeStatis',//采购费用统计
                    'purAnalysis',//采购费用分析
                )
            ),
            'OfflineSuppliers' => array(
                'OfflineSuppliers' => array(
                    'offlineSuppliersList',//厂商列表
                    'addOfflineSupplier',//新增厂商
                    'editOfflineSupplier',//维护厂商信息
                    'olsContract',//合同管理
                    'payOLSContractList',//合同付款管理
                    'addOLSContract',//新增合同
                    'confirmOLSContract',//确认合同
                    'payOLSContract',//合同付款
                    'delSupplier',//删除厂商
                )
            ),
            'BaseSetting'=>array(
                'IntegratedSetting'=>array(
                    'department',//科室设置
                    'category',//主设备分类设置
                    'system',//微信参数设置
                    'operationLog',//用户行为日志
                    'addCategory',//添加主设备分类
                    'editCategory',//修改分类
                    'deleteCategory',//删除分类
                    'batchAddCategory',//批量添加分类
                    'addDepartment',//添加科室
                    'editDepartment',//修改科室
                    'deleteDepartment',//删除科室
                    'batchAddDepartment',//批量添加科室
                ),
                'Privilege'=>array(
                    'getRoleList',//角色管理
                    'editRoleUser',//成员维护
                    'addRole',//添加角色
                    'editRolePrivi',//权限维护
                    'editRole',//编辑角色
                    'deleteRole',//删除角色
                ),
                'User'=>array(
                    'getUserList',//用户管理
                    'addUser',//新增用户
                    'editUser',//修改用户
                    'deleteUser',//删除用户
                    'clearOpenid',//微信解绑
                    'batchDeleteUser',//批量删除用户
                    'batchAddUser',//批量添加用户
                    'uploadautograph',//上传用户签名
                ),
                'ApproveSetting'=>array(
                    'editProcess',//修改流程
                    'deleteProcess',//删除流程
                    'addProcess',//添加流程
                    'approveLists',//多级审批设置
                ),
                'Notice'=>array(
                    'addNotice',//发布公告
                    'editNotice',//编辑公告
                    'deleteNotice',//删除公告
                    'getNoticeList',//公告管理
                ),
                'Dictionary'=>array(
                    'assetsDic',//设备字典
                    'addAssetsDic',//新增设备字典
                    'editAssetsDic',//修改设备字典
                    'delAssetsDic',//删除设备字典
                    'partsDic',//配件字典
                    'addPartsDic',//新增配件字典
                    'editPartsDic',//修改配件字典
                    'delPartsDic',//删除配件字典
                    'brandDic',//品牌字典
                    'addBrandDic',//新增品牌
                    'editBrandDic',//修改品牌
                    'delBrandDic',//删除品牌
                    'batchAddAssetsDic',//批量添加设备字典
                ),
                'SmsModule'=>array(
                    'smsSetting',//短信配置
                ),
                'ExamApp'=>array(
                    'getExamLists',//审查批准
                    'passno',//同意/驳回
                )
            ),
            'Archives' => array(
                'Emergency'=>array(
                    'emergencyPlanList',//应急预案列表
                    'addEmergency',//添加应急预案
                    'editEmergency',//修改应急预案
                    'delEmergency',//删除应急预案
                )
            )
        ),
    ),
    '5' => array(
        'name' => '系统管理员',
        'role' => array(
            'Assets' => array(
                'Scrap' => array(
                    'getApplyList',//报废申请列表
                    'applyScrap',//报废申请
                    'getExamineList',//报废审核列表
                    'examine',//报废审核
                    'getScrapList',//报废查询
                    'getResultList',//报废结果
                    'result',//报废处置
                ),
                'Lookup' => array(
                    'getAssetsList',//设备查询列表
                    'showAssetsPrice',//显示设备原值
                    'assetsLifeList',//设备生命历程
                    'getInsuranceList',//设备参保列表
                    'doRenewal',//续保
                    'batchAddAssets',//批量添加主设备
                    'addAssets',//添加主设备
                    'editAssets',//修改主设备
                    'deleteAssets',//删除主设备
                    'batchEditAssets',//批量修改主设备
                    'exportAssets',//导出主设备
                ),
                'Transfer' => array(
                    'getList',//转科申请
                    'add',//转科
                    'batchAdd',//批量转移科室
                    'progress',//转科进程
                    'examine',//转科审批
                    'approval',//审批
                    'checkLists',//转科验收
                    'check',//验收
                ),
                'Statistics' => array(
                    'onUseAssetsSurvey',//在用设备统计
                    'assetsSummary',//设备汇总统计
                    'assetsSurvey',//设备概况
                    'exportAssetsSurvey',//导出设备概况
                    'exportUsingAssetsSurvey',//导出在用设备概况
                    'exportAssetsSummary',//导出设备汇总统计概况
                    'exportDepartmentSummary',//导出部门电子账单
                    'exportRepairRecord',//导出设备维修记录
                    'MaintenanceSummary',//参保信息统计
                ),
                'Borrow' => array(
                    'borrowAssetsList',//借调申请列表
                    'applyBorrow',//申请
                    'borrowLife',//借调进程
                    'approveBorrowList',//借调审批列表
                    'departApproveBorrow',//借调部门审批
                    'assetsApproveBorrow',//设备科审批
                    'borrowInCheckList',//借入验收列表
                    'borrowInCheck',//借入验收
                    'giveBackCheckList',//归还验收列表
                    'giveBackCheck',//归还验收
                    'borrowRecordList',//归还验收
                ),
                'Outside' => array(
                    'getDepartAssetsList',//外调申请列表
                    'applyAssetOutSide',//申请
                    'outSideApproveList',//外调审批列表
                    'assetOutSideApprove',//审批
                    'outSideResultList',//外调结果
                    'checkOutSiteAsset',//验收单录入
                ),
                'Subsidiary' => array(
                    'subsidiaryAllotList',//分配申请列表
                    'subsidiaryAllot',//申请
                    'subsidiaryApproveList',//分配审批列表
                    'subsidiaryApprove',//审批
                    'subsidiaryCheckList',//分配验收列表
                    'subsidiaryCheck',//验收
                ),
                'Print' => array(
                    'design',//标签设计
                    'printAssets',//标签打印
                ),
                'AssetsSetting'=>array(
                    'assetsModuleSetting',//模块配置
                )
            ),
            'Repair' => array(
                'Repair' => array(
                    'ordersLists',//接单列表
                    'getRepairLists',//维修处理列表
                    'accept',//接单
                    'startRepair',//开始维修
                    'uploadRepair',//上传维修文件
                    'progress',//维修进程列表
                    'overhaul',//检修
                    'batchAddRepair',//集中录入
                    'dispatchingLists',//派工响应列表
                    'assigned',//指派
                    'getAssetsLists',//设备报修列表
                    'addRepair',//报修
                    'repairApproveLists',//维修审批列表
                    'addApprove',//审批
                    'examine',//验收列表
                    'checkRepair',//验收
                ),
                'RepairSearch'=>array(
                    'getRepairSearchList',//维修记录列表
                ),
                'Statistics'=>array(
                    'faultSummary',//设备故障统计
                    'exportFaultSummary',//导出故维修障统计表格
                    'exportFaultDetailed',//导出设备故障明细表格
                ),
                'RepairSetting'=>array(
                    'typeSetting',//故障类型设置
                    'addType',//添加故障类型
                    'editProblem',//修改故障问题
                    'deleteProblem',//删除故障问题
                    'repairAssign',//维修自动派工
                    'repairModuleSetting',//模块配置
                ),
                'RepairParts'=>array(
                    'partsInWareList',//配件入库列表
                    'partsInWare',//配件入库
                    'partsOutWareList',//配件出库列表
                    'partsOutWare',//配件库存
                    'partStockList',//配件库存列表
                )
            ),
            'Patrol'=>array(
                'Patrol'=>array(
                    'tasksList',//任务实施列表
                    'doTask',//执行任务
                    'patrolList',//计划查询列表
                    'addPatrol',//新增巡查保养
                    'editPatrol',//修订计划
                    'releasePatrol',//发布计划
                    'examineList',//科室验收列表
                    'examine',//巡查验收
                    'patrolApprove',//巡查计划审核
                    'approve',//审核
                ),
                'PatrolRecords'=>array(
                    'getPatrolRecords',//设备保养记录查询
                    'printReports',//批量打印保养报告
                    'exportReports',//批量导出
                ),
                'Statistics'=>array(
                    'patrolPlanSurvey',//巡查计划概况
                    'exportPlanSummary',//导出巡查计划概况
                    'exportPlanLists',//导出设备计划记录
                ),
                'PatrolSetting'=>array(
                    'template',//模板维护
                    'initialization',//设备初始化模板
                    'batchSettingTemplate',//设定模板
                    'addTemplate',//新增模板
                    'points',//保养项目类别&明细
                    'addPoints',//新增类别
                    'editDetail',//修改明细
                    'deleteDetail',//删除明细
                    'deleteTemplate',//删除模板
                    'editTemplate',//编辑模板
                ),
                'PatModSetting'=>array(
                    'patrolModuleSetting',//巡查模块配置
                )
            ),
            'Qualities'=>array(
                'Quality'=>array(
                    'getQualityList',//质控计划制定
                    'addQuality',//新增质控计划
                    'startQualityPlan',//启用质控计划
                    'editQualityPlan',//编辑质控计划
                    'feedbackQuality',//接收每日质控反馈
                    'showQualityPlan',//质控计划详情
                    'qualityDetailList',//明细录入列表
                    'setQualityDetail',//明细录入/执行计划
                    'qualityResult',//质控结果查询
                    'getDetectingList',//检测仪器管理
                    'presetQualityItem',//设备质控项目预设
                    'addPresetQI',//质控项目设置
                    'editQualityDetail',//修改明细
                    'Statistics',//质控项目设置
                ),
                'QualityStatistics'=>array(
                    'qualityDepartStatistics',//科室质控报表
                    'exportDepartStatistics',//导出报表
                )
            ),
            'Adverse'=>array(
                'Adverse'=>array(
                    'getAdverseList',//器械不良事件报告列表
                    'addAdverse',//新增不良事件报告
                    'editAdverse',//编辑不良事件报告
                )
            ),
            'Benefit' => array(
                'Benefit' => array(
                    'assetsBenefitList',//设备收支录入
                    'singleBenefitList',//单机数据分析
                    'departmentBenefitList',//科室数据分析
                    'batchAddBenefit',//批量录入收支明细
                    'exportBenefit',//批量导出收支明细
                )
            ),
            'Metering' => array(
                'Metering' => array(
                    'getMeteringList',//计量计划制定
                    'addMetering',//新增计量计划
                    'batchAddMetering',//批量新增计量计划
                    'saveMetering',//编辑
                    'delMetering',//删除
                    'getMeteringResult',//计量检定结果
                    'setMeteringResult',//检测
                )
            ),
            'Statistics' => array(
                'AdverseStatis' => array(
                    'adverseAnalysis',//不良事件报表分析
                ),
                'QualityStatis' => array(
                    'qualityAnalysis',//质控计划报表分析
                    'resultAnalysis',//质控结果报表分析
                ),
                'RepairStatis' => array(
                    'repairFeeStatis',//维修费用统计
                    'repairAnalysis',//维修费用分析
                    'engineerCompar',//工程师工作量对比
                    'engineerEva',//工程师评价对比
                    'repairFeeTrend',//维修设备趋势分析
                ),
                'PurchasesStatis' => array(
                    'purFeeStatis',//采购费用统计
                    'purAnalysis',//采购费用分析
                )
            ),
            'OfflineSuppliers' => array(
                'OfflineSuppliers' => array(
                    'offlineSuppliersList',//厂商列表
                    'addOfflineSupplier',//新增厂商
                    'editOfflineSupplier',//维护厂商信息
                    'olsContract',//合同管理
                    'payOLSContractList',//合同付款管理
                    'addOLSContract',//新增合同
                    'confirmOLSContract',//确认合同
                    'payOLSContract',//合同付款
                    'delSupplier',//删除厂商
                )
            ),
            'BaseSetting'=>array(
                'IntegratedSetting'=>array(
                    'department',//科室设置
                    'category',//主设备分类设置
                    'system',//微信参数设置
                    'operationLog',//用户行为日志
                    'addCategory',//添加主设备分类
                    'editCategory',//修改分类
                    'deleteCategory',//删除分类
                    'batchAddCategory',//批量添加分类
                    'addDepartment',//添加科室
                    'editDepartment',//修改科室
                    'deleteDepartment',//删除科室
                    'batchAddDepartment',//批量添加科室
                ),
                'Privilege'=>array(
                    'getRoleList',//角色管理
                    'editRoleUser',//成员维护
                    'addRole',//添加角色
                    'editRolePrivi',//权限维护
                    'editRole',//编辑角色
                    'deleteRole',//删除角色
                ),
                'User'=>array(
                    'getUserList',//用户管理
                    'addUser',//新增用户
                    'editUser',//修改用户
                    'deleteUser',//删除用户
                    'clearOpenid',//微信解绑
                    'batchDeleteUser',//批量删除用户
                    'batchAddUser',//批量添加用户
                    'uploadautograph',//上传用户签名
                ),
                'ApproveSetting'=>array(
                    'editProcess',//修改流程
                    'deleteProcess',//删除流程
                    'addProcess',//添加流程
                    'approveLists',//多级审批设置
                ),
                'Notice'=>array(
                    'addNotice',//发布公告
                    'editNotice',//编辑公告
                    'deleteNotice',//删除公告
                    'getNoticeList',//公告管理
                ),
                'Dictionary'=>array(
                    'assetsDic',//设备字典
                    'addAssetsDic',//新增设备字典
                    'editAssetsDic',//修改设备字典
                    'delAssetsDic',//删除设备字典
                    'partsDic',//配件字典
                    'addPartsDic',//新增配件字典
                    'editPartsDic',//修改配件字典
                    'delPartsDic',//删除配件字典
                    'brandDic',//品牌字典
                    'addBrandDic',//新增品牌
                    'editBrandDic',//修改品牌
                    'delBrandDic',//删除品牌
                    'batchAddAssetsDic',//批量添加设备字典
                ),
                'SmsModule'=>array(
                    'smsSetting',//短信配置
                ),
                'ExamApp'=>array(
                    'getExamLists',//审查批准
                    'passno',//同意/驳回
                ),
                'ModuleSetting'=>array(
                    'module',//模块配置
                ),
                'Menu'=>array(
                    'getMenuLists',//菜单管理
                )
            ),
            'Archives' => array(
                'Emergency'=>array(
                    'emergencyPlanList',//应急预案列表
                    'addEmergency',//添加应急预案
                    'editEmergency',//修改应急预案
                    'delEmergency',//删除应急预案
                )
            )
        ),
    ),
);
?>