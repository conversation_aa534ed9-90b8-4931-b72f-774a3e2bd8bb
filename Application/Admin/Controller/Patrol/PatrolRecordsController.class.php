<?php

namespace Admin\Controller\Patrol;

use Admin\Controller\Login\CheckLoginController;
use Admin\Model\PatrolExecuteModel;
use Admin\Model\PatrolPlanModel;
use Admin\Model\UserModel;
use Admin\Model\PatrolModel;

class PatrolRecordsController extends CheckLoginController
{

    public function getPatrolRecords()
    {
        $planModel = new PatrolPlanModel();
        if (IS_POST) {
            $action = I('post.action');
            switch ($action) {
                case 'downpdf':
                    $this->downpdf();
                    break;
                case 'level_list':
                    $result = $this->level_list();
                    $this->ajaxReturn($result, 'json');
                    break;
                default:
                    $result = $planModel->getRecordSearchListData();
                    $this->ajaxReturn($result, 'json');
                    break;
            }
        } else {
            $action = I('get.action');
            switch ($action) {
                case 'showPatrolRecord':
                    $result = $planModel->showPatrolRecordData();
                    $this->assign('getPatrolRecords', get_url());
                    $this->assign('result', $result);
                    $this->display('showPatrolRecord');
                    break;
                case 'getReports':
                    $this->getReports();
                    break;
                default:
                    $UserModel = new UserModel();
                    //获取用户
                    $userInfo = $UserModel->getUsers('doTask', '', true, true);
                    //获取所有部门
                    $department = $UserModel->getAllDepartments(['hospital_id' => session('current_hospitalid'), 'is_delete' => C('NO_STATUS')]);
                    $patrolCycle_data = $UserModel->DB_get_all('assets_info', 'patrol_xc_cycle', array('is_delete' => 0), 'patrol_xc_cycle', 'patrol_xc_cycle');
                    $timeKey = array_column($patrolCycle_data, 'patrol_xc_cycle');
                    array_multisort($timeKey, SORT_DESC, $patrolCycle_data);
                    $maintainCycle_data = $UserModel->DB_get_all('assets_info', 'patrol_pm_cycle', array('is_delete' => 0), 'patrol_pm_cycle', 'patrol_pm_cycle');
                    $timeKey = array_column($maintainCycle_data, 'patrol_pm_cycle');
                    array_multisort($timeKey, SORT_DESC, $maintainCycle_data);
                    $this->assign('patrolCycle_data', $patrolCycle_data);
                    $this->assign('maintainCycle_data', $maintainCycle_data);
                    $this->assign('getRecordSearchList', get_url());
                    $this->assign('departmentInfo', $department);
                    $this->assign('userInfo', $userInfo);
                    $this->display();
                    break;
            }
        }
    }

    /*
    查看计划报告(巡查记录)
     */
    private function getReports()
    {
        $cycids = I('get.cycids');
        $assnum = I('get.assnum');
        $planModel = new PatrolPlanModel();
        $result = $planModel->getRecordDatas($assnum, $cycids);
        $data_left = $data_right = [];
        foreach ($result['data'] as $k => $v) {
            if ($k % 2 == 0) {
                $data_left[] = $v;
            } else {
                $data_right[] = $v;
            }
        }
        $PatrolModel = new PatrolModel();
        $apps = $PatrolModel->get_approve_info($result['plan_data']['patrid']);
        $title = $PatrolModel->getprinttitle('patrol', 'patrol_template');
        $this->assign('title', $title);
        $this->assign('result', $result);
        //工程师签名
        $engineer = $PatrolModel->get_autograph($result['plan_data']['execute_user']);
        //验收人签名
        $this->assign('engineer', $engineer);
        $this->assign('engineer_time', substr($result['plan_data']['finish_time'], 0, 10));
        //设备科签名
        $device = $PatrolModel->get_assets_autograph();
        $this->assign('device', $device);
        $this->assign('device_time', $result['plan_data']['cycle_end_date']);
        $departautograph = $PatrolModel->get_autograph($result['plan_data']['exam_user']);
        $this->assign('departautograph', $departautograph);
        $this->assign('departautograph_time', substr($result['plan_data']['exam_time'], 0, 10));
        $this->assign('data', $result['data']);
        $this->assign('data_left', $data_left);
        $this->assign('data_right', $data_right);
        $this->assign('apps', $apps);
        $this->assign('plan_data', $result['plan_data']);
        $this->assign('time', date('Y-m-d', time()));
        $baseSetting = array();
        include APP_PATH . "Common/cache/basesetting.cache.php";
        $this->assign('imageUrl', $baseSetting['all_module']['all_report_logo']['value']);
        $this->display('Patrol/ReportTemplate/patrolReport');
    }

    /**
     * 批量打印保养报告
     */
    public function printReports()
    {
        $printStyle = I('POST.printStyle');
        if ($printStyle == '1') {
            $this->onebatchPrintReport();
        } elseif ($printStyle == '2') {
            $this->allbatchPrintReport();
        } else {
            $this->assnumbatchPrintReport();
        }
    }

    /*
   打印一台设备的对应报告
    */
    public function assnumbatchPrintReport()
    {
        $assnum = I('POST.assnums');
        $cycids = I('POST.cycids');
        $cycids = trim($cycids, ',');
        $cycidArr = explode(',', $cycids);
        $html = "";
        $planModel = new PatrolPlanModel();
        $PatrolModel = new PatrolModel();
        foreach ($cycidArr as $key => $value) {
            $data_left = $data_right = [];
            $result = $planModel->getRecordDatas($assnum, $value);
            foreach ($result['data'] as $k => $v) {
                if ($k % 2 == 0) {
                    $data_left[] = $v;
                } else {
                    $data_right[] = $v;
                }
            }
            $apps = $PatrolModel->get_approve_info($result['plan_data']['patrid']);
            $title = $PatrolModel->getprinttitle('patrol', 'patrol_template');
            $this->assign('title', $title);
            $this->assign('result', $result);
            $this->assign('departautograph_time', substr($result['plan_data']['exam_time'], 0, 10));
            //经手工程师签名
            $engineer = $PatrolModel->get_autograph($result['plan_data']['execute_user']);
            $this->assign('engineer', $engineer);
            $this->assign('engineer_time', date('Y-m-d',strtotime($result['plan_data']['finish_time'])));
            //设备科签名
            $device = $PatrolModel->get_assets_autograph();
            $this->assign('device', $device);
            $this->assign('device_time', '');
            //验收科室签名
            $departautograph = $PatrolModel->get_autograph($result['plan_data']['exam_user']);
            $this->assign('departautograph', $departautograph);
            $this->assign('data', $result['data']);
            $this->assign('data_left', $data_left);
            $this->assign('data_right', $data_right);
            $this->assign('apps', $apps);
            $this->assign('min_height', '1330px');
            $this->assign('plan_data', $result['plan_data']);
            $this->assign('time', date('Y-m-d', time()));
            $marget_top = ($key + 2) % 2 == 0 ? 0 : 10;
            $this->assign('marget_top', $marget_top);
            if ($result['plan_data']) {
                $baseSetting = array();
                include APP_PATH . "Common/cache/basesetting.cache.php";
                $this->assign('imageUrl', $baseSetting['all_module']['all_report_logo']['value']);
                $html .= $this->display('Patrol/ReportTemplate/reportremp');
            }
        }
        echo $html;
        exit;
    }

    /*
    批量打印最近一次报告
     */
    public function onebatchPrintReport()
    {
        $assnum = I('POST.assnums');
        $assnum = trim($assnum, ',');
        $assnumArr = explode(',', $assnum);
        $html = "";
        $planModel = new PatrolPlanModel();
        $PatrolModel = new PatrolModel();
        foreach ($assnumArr as $key => $value) {
            //查询最近一次的保养DC或 PM
            $where = [];
            $where['assetnum'] = $value;
            $where['status'] = 2;
            $where['report_num'] = array('like', 'PM%');
            $execInfo = $planModel->DB_get_one('patrol_execute','execid,cycid',$where,'execid desc');

            $result = $planModel->getRecordData($value,$execInfo['cycid']);
            $data_left = $data_right = [];
            foreach ($result['data'] as $k => $v) {
                if ($k % 2 == 0) {
                    $data_left[] = $v;
                } else {
                    $data_right[] = $v;
                }
            }
            $apps = $PatrolModel->get_approve_info($result['plan_data']['patrid']);
            $title = $PatrolModel->getprinttitle('patrol', 'patrol_template');
            $this->assign('title', $title);
            $this->assign('result', $result);
            $this->assign('departautograph_time', substr($result['plan_data']['exam_time'], 0, 10));
            //经手工程师签名
            $engineer = $PatrolModel->get_autograph($result['plan_data']['execute_user']);
            $this->assign('engineer', $engineer);
            $this->assign('engineer_time', date('Y-m-d',strtotime($result['plan_data']['finish_time'])));
            //设备科签名
            $device = $PatrolModel->get_assets_autograph();
            $this->assign('device', $device);
            $this->assign('device_time', '');
            //验收科室签名
            $departautograph = $PatrolModel->get_autograph($result['plan_data']['exam_user']);
            $this->assign('departautograph', $departautograph);
            $this->assign('data', $result['data']);
            $this->assign('data_left', $data_left);
            $this->assign('data_right', $data_right);
            $this->assign('apps', $apps);
            $this->assign('plan_data', $result['plan_data']);
            $this->assign('time', date('Y-m-d', time()));
            $this->assign('min_height', '1200px');
            $marget_top = ($key + 2) % 2 == 0 ? 0 : 10;
            //$this->assign('marget_top', $marget_top);
            $baseSetting = array();
            include APP_PATH . "Common/cache/basesetting.cache.php";
            $this->assign('imageUrl', $baseSetting['all_module']['all_report_logo']['value']);
            $html .= $this->display('Patrol/ReportTemplate/reportremp');
        }
        echo $html;
        exit;
    }

    public function generate_pdf($assnum)
    {
        Vendor('mpdf.mpdf');
        //设置中文编码
        $mpdf = new \mPDF('zh-cn');
        $baseSetting = [];
        include APP_PATH . "Common/cache/basesetting.cache.php";
        if ($baseSetting['repair']['repair_tmp']['value']['style'] == 2) {
            //生成水印
            $water = C('WATER_NAME');
            if ($baseSetting['repair']['repair_print_watermark']['value']['watermark']) {
                $water = $baseSetting['repair']['repair_print_watermark']['value']['watermark'];
            }
            $mpdf->SetWatermarkText($water, 0.08);
        }
        //设置字体，解决中文乱码
        $mpdf->useAdobeCJK = TRUE;
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;
        //设置pdf显示方式
        $mpdf->SetDisplayMode('fullpage');
        //$strContent = '我是带水印的PDF文件';
        $mpdf->showWatermarkText = true;
        $mpdf->SetHTMLHeader('');
        $mpdf->SetHTMLFooter('');
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $url = "$protocol".C('HTTP_HOST') . C('ADMIN_NAME').'/Public/generate_pdf?assnum=' . $assnum;
        //$stylesheet = file_get_contents($url);
        $stylesheet = curl($url);
        $mpdf->WriteHTML($stylesheet);
        $dirName = 'patrol/zip/' . date('Ymd') . session('userid');
        $dir_path = './Public/uploads/' . $dirName;
        if (!file_exists($dir_path)) {
            //检查是否有该文件夹，如果没有就创建，并给予最高权限
            $dirarr = explode('/', $dirName);
            $tmpdir = './Public/uploads/';
            foreach ($dirarr as $v) {
                $tmpdir .= $v . '/';
                mkdir($tmpdir, 0777);
                chmod($tmpdir, 0777);
            }
        }
        is_directory($dir_path);
        $mpdf->Output($dir_path . '/DC' . $assnum . '.pdf', 'f');
        return $dir_path;
    }

    /*
     批量打印所有报告
      */
    public function allbatchPrintReport()
    {
        $assnum = I('POST.assnums');
        $assnum = trim($assnum, ',');
        $assnumArr = explode(',', $assnum);
        $html = "";
        $planModel = new PatrolPlanModel();
        $PatrolModel = new PatrolModel();
        foreach ($assnumArr as $key => $value) {
            //查询最近一次的保养DC或 PM
            $where = [];
            $where['assetnum'] = $value;
            $where['status'] = 2;
            $where['report_num'] = array('like', 'PM%');
            $execInfo = $planModel->DB_get_all('patrol_execute','execid,cycid',$where,'execid desc');
            foreach ($execInfo as $ke=>$kv){
                $result = $planModel->getRecordData($value,$kv['cycid']);
                $data_left = $data_right = [];
                foreach ($result['data'] as $k => $v) {
                    if ($k % 2 == 0) {
                        $data_left[] = $v;
                    } else {
                        $data_right[] = $v;
                    }
                }
                $apps = $PatrolModel->get_approve_info($result['plan_data']['patrid']);
                $title = $PatrolModel->getprinttitle('patrol', 'patrol_template');
                $this->assign('title', $title);
                $this->assign('result', $result);
                $this->assign('departautograph_time', substr($result['plan_data']['exam_time'], 0, 10));
                //经手工程师签名
                $engineer = $PatrolModel->get_autograph($result['plan_data']['execute_user']);
                $this->assign('engineer', $engineer);
                $this->assign('engineer_time', date('Y-m-d',strtotime($result['plan_data']['finish_time'])));
                //设备科签名
                $device = $PatrolModel->get_assets_autograph();
                $this->assign('device', $device);
                $this->assign('device_time', '');
                //验收科室签名
                $departautograph = $PatrolModel->get_autograph($result['plan_data']['exam_user']);
                $this->assign('departautograph', $departautograph);
                $this->assign('data', $result['data']);
                $this->assign('data_left', $data_left);
                $this->assign('data_right', $data_right);
                $this->assign('apps', $apps);
                $this->assign('min_height', '1200px');
                $this->assign('plan_data', $result['plan_data']);
                $this->assign('time', date('Y-m-d', time()));
                $marget_top = ($key + 2) % 2 == 0 ? 0 : 10;
                //$this->assign('marget_top', $marget_top);
                $baseSetting = array();
                include APP_PATH . "Common/cache/basesetting.cache.php";
                $this->assign('imageUrl', $baseSetting['all_module']['all_report_logo']['value']);
                $html .= $this->display('Patrol/ReportTemplate/reportremp');
            }
        }
        echo $html;
        exit;
    }

    /**
     * 导出excel
     */
    public function exportReports()
    {
        $type = I('POST.type');
        if($type == 'departmentRecord'){
            //导出科室数据
            $this->exportDepartmentRecord();
        }else{
            $planModel = new PatrolPlanModel();
            $assid = explode(',', trim(I('POST.assid'), ','));
            if (!$assid) {
                $this->error('参数错误！');
                exit;
            }
            //获取要导出的数据
            //读取assets、factory数据库字段
            $field = 'assets,assnum,assorignum,model,departid,serialnum,patrol_xc_cycle,patrol_pm_cycle,patrol_nums,maintain_nums,patrol_dates,maintain_dates,pre_patrol_executor,pre_patrol_result,pre_maintain_result,pre_maintain_date';
            $data = $planModel->DB_get_all('assets_info', $field, ['assid' => ['IN', $assid]], '', 'adddate desc');
            $departname = array();
            include APP_PATH . "Common/cache/department.cache.php";
            $assnums = "";
            foreach ($data as $k => $v) {
                $data[$k]['xuhao'] = $k + 1;
                $data[$k]['department'] = $departname[$v['departid']]['department'];
                $assnums = $assnums . $v['assnum'] . ',';
            }
            $assnums = substr($assnums, 0, -1);
            $execute_data = $planModel->DB_get_all_join('patrol_execute', 'A', 'A.report_num,A.assetnum,A.cycid,A.finish_time,A.execute_user,A.asset_status,B.complete_time,B.cycle_start_date,B.cycle_end_date', "LEFT JOIN sb_patrol_plans_cycle as B ON A.cycid=B.cycid LEFT JOIN sb_patrol_plans as C ON C.patrid=B.patrid", ['assetnum' => ['IN', $assnums]]);
            $exec_data = array();
            foreach ($execute_data as $k => $v) {
                $exec_data[$v['assetnum']] = $v;
            }
            foreach ($data as $key => $value) {
                $data[$key]['report_num'] = $exec_data[$value['assnum']]['report_num'];
                $data[$key]['executor'] = $exec_data[$value['assnum']]['execute_user'];
                $data[$key]['asset_status'] = $exec_data[$value['assnum']]['asset_status'];
                $data[$key]['complete_time'] = $exec_data[$value['assnum']]['complete_time'];
                $data[$key]['executedate'] = $exec_data[$value['assnum']]['cycle_start_date'];
                if (strtotime($exec_data[$value['assnum']]['complete_time']) - strtotime($exec_data[$value['assnum']]['cycle_end_date']) > 24 * 60 * 60) {
                    $data[$key]['is_overdue'] = '逾期';
                } else {
                    $data[$key]['is_overdue'] = '未逾期';
                }
                if (!isset($data[$key]['pre_maintain_date'])) {
                    $data[$key]['next_maintain_date'] = $data[$key]['executedate'];
                } else {
                    $data[$key]['next_maintain_date'] = date("Y-m-d", strtotime($data[$key]['pre_maintain_date']) + $data[$key]['patrol_pm_cycle'] * 24 * 60 * 60);
                }
                $dir_path = $this->generate_pdf($value['assnum']);
            }
            //下次保养日期=上次保养日期+周期(天)
            //格式化数据
            $showName = [
                'xuhao' => '序号',
                'report_num' => '设备保养报告编号',
                'executor' => '经办工程师',
                'department' => '使用科室',
                'assets' => '设备名称',
                'assnum' => '设备编号',
                'assorignum' => '设备原编码',
                'model' => '规格/型号',
                'serialnum' => '固定资产编号',
                'patrol_pm_cycle' => '保养周期（天）',
                'next_maintain_date' => '下次保养日期',
                'executedate' => '计划保养日期',
                'complete_time' => '实际保养时间',
                'is_overdue' => '是否逾期（天）',
                'asset_status' => '保养结果',
            ];
            exportPatrolReport([date('Y', time()) . '设备预防性维护管控表'], date('Y', time()) . '设备预防性维护管控表', $showName, $data, $dir_path);
        }
    }

    /**
     * 导出科室数据
     */
    public function exportDepartmentRecord()
    {
        $departid = I('POST.departid');
        $level = I('POST.level');
        $startDate = I('POST.startDate');
        $endDate = I('POST.endDate');
        $where['A.status'] = 2;
        if($departid){
            $where['B.departid'] = $departid;
        }else{
            exit('请选择科室');
        }
        if($level){
            $where['D.patrol_level'] = $level;
        }
        if($startDate != '' && $endDate == ''){
            $where['A.finish_time'] = array('EGT',$startDate.' 00:00:00');
        }elseif($endDate != '' && $startDate == ''){
            $where['A.finish_time'] = array('ELT',$endDate.' 23:59:59');
        }elseif($startDate != '' && $endDate != ''){
            $where['A.finish_time'] = array(array('EGT',$startDate.' 00:00:00'),array('ELT',$endDate.' 23:59:59'),'and');
        }
        $fields = 'A.execid,A.cycid,B.assnum,B.assets,A.asset_status,A.finish_time,B.assets,B.model,B.departid,A.execute_user,C.patrol_num';
        $join[0] = ' LEFT JOIN sb_assets_info AS B ON A.assetnum = B.assnum ';
        $join[1] = ' LEFT JOIN sb_patrol_plans_cycle AS C ON A.cycid = C.cycid ';
        $join[2] = ' LEFT JOIN sb_patrol_plans AS D ON C.patrid = D.patrid ';
        $executeModel = new PatrolExecuteModel();
        $data = $executeModel->DB_get_all_join('patrol_execute','A',$fields,$join,$where,'','');
        //var_dump(M()->getLastSql());
        if(!$data){
            exit('暂无相关数据');
        }
        $departname = array();
        include APP_PATH . "Common/cache/department.cache.php";
        foreach ($data as $k=>$v){
            $data[$k]['xuhao'] = $k + 1;
            $data[$k]['department'] = $departname[$v['departid']]['department'];
            $data[$k]['finish_time'] = date('Y-m-d H:i',strtotime($v['finish_time']));
            $total = $executeModel->DB_get_count('patrol_execute_abnormal',['execid'=>$v['execid']]);
            $error = $executeModel->DB_get_count('patrol_execute_abnormal',['execid'=>$v['execid'],'result'=>array('neq','合格')]);
            $data[$k]['error_total'] = $error.'/'.$total;
        }
        //格式化数据
        $expCellName = [
            'xuhao' => '序号',
            'patrol_num' => '计划编号',
            'assets' => '设备名称',
            'assnum' => '设备编号',
            'model' => '规格型号',
            'department' => '使用科室',
            'execute_user' => '执行人',
            'asset_status' => '执行结果',
            'finish_time' => '完成时间',
            'error_total' => '异常项/明细项',
        ];
        exportAssets(array('单科室设备保养记录表'),'单科室设备保养记录表',$expCellName,$data);
    }

    /*
     下载一个pdf文件
     */
    private function downpdf()
    {
        $assnum = I('POST.assnum');
        $cycid = I('POST.cycid');
        Vendor('mpdf.mpdf');
        //设置中文编码
        $mpdf = new \mPDF('zh-cn');
        $baseSetting = [];
        include APP_PATH . "Common/cache/basesetting.cache.php";
        if ($baseSetting['repair']['repair_tmp']['value']['style'] == 2) {
            //生成水印
            $water = C('WATER_NAME');
            if ($baseSetting['repair']['repair_print_watermark']['value']['watermark']) {
                $water = $baseSetting['repair']['repair_print_watermark']['value']['watermark'];
            }
            $mpdf->SetWatermarkText($water, 0.08);
        }
        //设置字体，解决中文乱码
        $mpdf->useAdobeCJK = TRUE;
        $mpdf->autoScriptToLang = true;
        $mpdf->autoLangToFont = true;
        //设置pdf显示方式
        $mpdf->SetDisplayMode('fullpage');
        //$strContent = '我是带水印的PDF文件';
        $mpdf->showWatermarkText = true;
        $mpdf->SetHTMLHeader('');
        $mpdf->SetHTMLFooter('');
        $protocol = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off' || $_SERVER['SERVER_PORT'] == 443) ? "https://" : "http://";
        $url = "$protocol".C('HTTP_HOST') . C('ADMIN_NAME').'/Public/generates_pdf?assnum=' . $assnum . '&cycid=' . $cycid;
        $stylesheet = file_get_contents($url);
        $mpdf->WriteHTML($stylesheet);
        $mpdf->Output('设备【' . $assnum . '】保养报告.pdf', 'D');
    }

    public function level_list()
    {
        $limit = I('post.limit') ? I('post.limit') : 10;
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $order = I('post.order') ? I('post.order') : 'desc';
        $sort = I('post.sort') ? I('post.sort') : 'A.finish_time';
        $level = I('post.level');
        $assnum = I('post.assnum');
        $finish_time = I('post.finish_time');
        $where = [];
        if ($finish_time) {
            $start = $finish_time . '-01 00:00:01';
            $end = date("Y-m-d", strtotime("+1 month", strtotime($finish_time)) - 86400) . ' 23:59:59';
            $where['A.finish_time'] = array(array('egt', $start), array('elt', $end));
        }
        $fields = "A.execid,A.cycid,A.assetnum,A.report_num,A.finish_time,A.asset_status,A.execute_user,B.patrol_num,B.period,C.patrol_name,C.patrol_level,COUNT(abnid) AS detail";
        $join[0] = ' LEFT JOIN sb_patrol_plans_cycle AS B ON A.cycid = B.cycid ';
        $join[1] = ' LEFT JOIN sb_patrol_plans AS C ON B.patrid = C.patrid ';
        $join[2] = ' LEFT JOIN sb_patrol_execute_abnormal AS D ON A.execid = D.execid ';

        $PatrolModel = new PatrolModel();
        $assInfo = $PatrolModel->DB_get_one('assets_info', 'assid,assnum,assets,model,departid', ['assnum' => $assnum]);
        //查询巡查信息
        switch ($level) {
            case 1:
                $where['A.report_num'] = array(array('like', 'DC%'));
                $where['A.assetnum'] = $assnum;
                break;
            case 2:
                $where['A.report_num'] = array(array('like', 'RC%'));
                $where['A.assetnum'] = $assnum;
                break;
            case 3:
                $where['A.report_num'] = array(array('like', 'PM%'));
                $where['A.assetnum'] = $assnum;
                break;
        }
        //查询信息
        $total = $PatrolModel->DB_get_all_join('patrol_execute', 'A', $fields, $join, $where, 'B.cycid');
        $maintainInfo = $PatrolModel->DB_get_all_join('patrol_execute', 'A', $fields, $join, $where, 'B.cycid', $sort . ' ' . $order,$offset . "," . $limit);
        if (!$maintainInfo) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        foreach ($maintainInfo as $key => $value) {
            $maintainInfo[$key]['assets'] = $assInfo['assets'];
            $maintainInfo[$key]['assnum'] = $assInfo['assnum'];
            $maintainInfo[$key]['model'] = $assInfo['model'];
            $maintainInfo[$key]['department'] = $departname[$assInfo['departid']]['department'];
            switch ($value['patrol_level']) {
                case C('PATROL_LEVEL_PM')://3=预防性维护(PM)
                    $maintainInfo[$key]['patrol_laravel_name'] = '预防性维护(PM)';
                    break;
                case C('PATROL_LEVEL_RC')://2巡查保养(RC)
                    $maintainInfo[$key]['patrol_laravel_name'] = '巡查保养(RC)';
                    break;
                case C('PATROL_LEVEL_DC')://1日常保养(DC)
                    $maintainInfo[$key]['patrol_laravel_name'] = '日常保养(DC)';
                    break;
            }
            $maintainInfo[$key]['finish_time'] = date('Y-m-d H:i', strtotime($value['finish_time']));
            if ($value['detail'] > 0) {
                $maintainInfo[$key]['unusual'] = $PatrolModel->DB_get_count('patrol_execute_abnormal', array('execid' => $value['execid'], 'result' => array('neq', '合格')));
            } else {
                unset($maintainInfo[$key]);
            }
            $maintainInfo[$key]['unusual_detail'] = $maintainInfo[$key]['unusual'] . '/' . $value['detail'];
            $html = '<div class="layui-btn-group">';
            $html .= '<button class="layui-btn layui-btn-xs layui-btn-normal" lay-event="showRecord" data-url="/A/PatrolRecords/getPatrolRecords.html?action=getReports&cycids=' . $value['cycid'] . '&assnum=' . $assnum . '">查看</button>';
            $html .= '<button class="layui-btn layui-btn-xs" lay-event="downRecord" data-url="/A/PatrolRecords/getPatrolRecords.html" data-id="' . $value['cycid'] . '">下载</button>';
            $html .= '</div>';
            $maintainInfo[$key]['operation'] = $html;
        }

        $result["total"] = count($total);
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["rows"] = $maintainInfo;
        $result["code"] = 200;
        return $result;
    }
}
