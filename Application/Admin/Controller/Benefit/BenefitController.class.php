<?php

namespace Admin\Controller\Benefit;

use Admin\Controller\Login\CheckLoginController;
use Admin\Controller\NotCheckLogin\PublicController;
use Admin\Model\AssetsInfoModel;
use Admin\Model\BenefitModel;
use Admin\Model\DepartmentModel;
use Admin\Model\RepairModel;

class BenefitController extends CheckLoginController
{

    public function index()
    {
        $this->display();
    }

    //设备收支录入
    public function assetsBenefitList()
    {
        if (IS_POST) {
            $type = I('POST.type');
            $BenefitModel = new BenefitModel();
            if ($type == 'updateData') {
                $result = $BenefitModel->updateBenefitData();
                $this->ajaxReturn($result, 'json');
            } else {
                $result = $BenefitModel->assetsBenefitList();
                $this->ajaxReturn($result, 'json');
            }
        } else {
            //所属科室
            $notCheck = new PublicController();
            $this->assign('departmentInfo', $notCheck->getAllDepartmentSearchSelect());
            $this->display();
        }

    }

    //单机效益分析
    public function singleBenefitList()
    {
        if (IS_POST) {
            $BenefitModel = new BenefitModel();
            $result = $BenefitModel->singleBenefitList();
            $this->ajaxReturn($result, 'json');
        } else {
            $this->assign('preMonth', date("Y-m", strtotime("-1 month")));
            $this->display();
        }
    }


    //单机效益分析
    public function assetsBenefitData()
    {
        if (IS_POST) {
            $BenefitModel = new BenefitModel();
            $type = I('POST.type');
            if($type == 'getDetail'){
                $result = $BenefitModel->getBenfitDetail();
            }else{
                $result = $BenefitModel->assetsBenefitDataGetFix();
            }
            $this->ajaxReturn($result, 'json');
        } else {
            $assid = I('get.assid');
            $sdate = I('get.sdate');
            $edate = I('get.edate');
            $RepairMod = new RepairModel();
            $asArr = $RepairMod->getAssetsBasic($assid);
            //统计总收支情况
            $fields = "assnum,entryDate,income,depreciation_cost,material_cost,maintenance_cost,management_cost,comprehensive_cost,interest_cost,work_number";
            $where['assnum'] = array('EQ', $asArr['assnum']);
            $asArr['is_huiben'] = '-';//是否回本
            $asArr['huiben_date'] = '-';//回本日期
            if($asArr['buy_price'] > 0){
                $income = $all_cost = 0;
                $fee = $RepairMod->DB_get_all('assets_benefit', $fields, $where, '', 'entryDate ASC');
                foreach ($fee as $v) {
                    $income += $v['income'];
                    $all_cost += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                }
                if (($income - $all_cost) >= $asArr['buy_price']){
                    $asArr['is_huiben'] = '<span style="color: #009688">已回本</span>';
                    $income = $all_cost = 0;
                    foreach ($fee as $v) {
                        $income += $v['income'];
                        $all_cost += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                        if (($income - $all_cost) - $asArr['buy_price'] >= 0){
                            $asArr['huiben_date'] = $v['entryDate'];
                            break;
                        }
                    }
                }else{
                    $asArr['is_huiben'] = '<span style="color: #FF5722">未回本</span>';
                }
            }
            $pic = json_decode($asArr['pic_url']);
            $asArr = $RepairMod->formatRepair($asArr);
            $this->assign('sdate', $sdate);
            $this->assign('edate', $edate);
            $this->assign('asArr', $asArr);
            $this->assign('pic', $pic);
            $this->assign('getBenefitLists', get_url());
            $this->display();
        }
    }

    //科室效益分析列表
    public function departmentBenefitList()
    {
        if (IS_POST) {
            $BenefitModel = new BenefitModel();
            $result = $BenefitModel->departmentBenefitList();
            $this->ajaxReturn($result, 'json');
        } else {
            $this->display();
        }
    }

    //科室效益分析
    public function departmentBenefitData()
    {
        if (IS_POST) {
            $type = I('POST.type');
            $BenefitModel = new BenefitModel();
            switch ($type){
                case 'getHuibenAssets';
                    $result = $BenefitModel->getHuiBenAssets();
                    break;
                case 'getBenefitAssets';
                    $result = $BenefitModel->getBenfitAssets();
                    break;
                default:
                    $result = $BenefitModel->departmentBenefitData();
                    break;
            }
            $this->ajaxReturn($result, 'json');
        } else {
            $departid = I('get.departid');
            $where['hospital_id'] = session('current_hospitalid');
            $where['status'][0] = 'NOT IN';
            $where['status'][1][] = C('ASSETS_STATUS_SCRAP');
            $where['status'][1][] = C('ASSETS_STATUS_OUTSIDE');
            $where['status'][1][] = C('ASSETS_STATUS_OUTSIDE_ON');
            $where['is_delete'] = 0;
            $where['main_assid'] = 0;//主设备
            $where['departid'] = array('EQ', $departid);
            $assModel = new AssetsInfoModel();
            $assData = $assModel->DB_get_all('assets_info', 'assid,departid,buy_price,is_benefit', $where);
            $benefit_total_price = $benefit_nums = 0;
            foreach ($assData as $v){
                if($v['is_benefit'] == 1){
                    $benefit_nums += 1;
                    $benefit_total_price += $v['buy_price'];
                }
            }
            //统计效益设备收支
            $benefitModel = M('assets_benefit');
            $benefit_income = $benefitModel->where(['departid'=>$departid])->sum('income');
            $depreciation_cost = $benefitModel->where(['departid'=>$departid])->sum('depreciation_cost');
            $material_cost = $benefitModel->where(['departid'=>$departid])->sum('material_cost');
            $maintenance_cost = $benefitModel->where(['departid'=>$departid])->sum('maintenance_cost');
            $management_cost = $benefitModel->where(['departid'=>$departid])->sum('management_cost');
            $comprehensive_cost = $benefitModel->where(['departid'=>$departid])->sum('comprehensive_cost');
            $interest_cost = $benefitModel->where(['departid'=>$departid])->sum('interest_cost');

            $total_cost = $depreciation_cost + $material_cost + $maintenance_cost + $management_cost + $comprehensive_cost + $interest_cost;
            $benefit_profit = $benefit_income - $total_cost;
            $benefit_huiben = ($benefit_total_price - $benefit_profit) > 0 ? '未回本' : '已回本';

            $this->assign('departmentBenefitData', get_url());
            $this->assign('departid', $departid);
            $this->assign('depart_nums', count($assData));
            $this->assign('benefit_nums', $benefit_nums);
            $this->assign('benefit_total_price', round($benefit_total_price/10000,2));
            $this->assign('benefit_income', round($benefit_income/10000,2));
            $this->assign('benefit_cost', round($total_cost/10000,2));
            $this->assign('benefit_profit', round($benefit_profit/10000,2));
            $this->assign('benefit_huiben', $benefit_huiben);
            $this->display();
        }
    }

    //批量录入收支明细
    public function batchAddBenefit()
    {
        if (IS_POST) {
            $type = I('POST.type');
            switch ($type) {
                case 'save':
                    $BenefitModel = new BenefitModel();
                    $result = $BenefitModel->batchAddBenefit();
                    $this->ajaxReturn($result);
                    break;
                case 'getData':
                    //获取待入库设备明细信息
                    $BenefitModel = new BenefitModel();
                    $result = $BenefitModel->getWatingUploadBenefit();
                    $this->ajaxReturn($result);
                    break;
                case 'updateData':
                    //更新临时表数据库
                    $BenefitModel = new BenefitModel();
                    $result = $BenefitModel->updateTempData();
                    $this->ajaxReturn($result);
                    break;
                case 'delTmpBenefit':
                    //删除临时表数据库
                    $BenefitModel = new BenefitModel();
                    $result = $BenefitModel->delTempData();
                    $this->ajaxReturn($result);
                    break;
                case 'upload':
                    //接收上传文件数据
                    $BenefitModel = new BenefitModel();
                    $result = $BenefitModel->uploadData();
                    $this->ajaxReturn($result);
                    break;
                default:
                    $this->ajaxReturn(array('status' => -1, 'msg' => '空操作！'));
                    break;
            }
        } else {
            $type = I('GET.type');
            if ($type == 'exploreBenefitModel') {
                $BenefitModel = new BenefitModel();
                $BenefitModel->exploreBenefitModel();
            } else {
                //所属科室
                $notCheck = new PublicController();
                $this->assign('departmentInfo', $notCheck->getAllDepartmentSearchSelect());
                $this->display();
            }
        }
    }


    //批量导出收支明细
    public function exportBenefit()
    {
        $BenefitModel = new BenefitModel();
        $BenefitModel->exportBenefit();
    }

}
