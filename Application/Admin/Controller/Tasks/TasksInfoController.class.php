<?php

/**
 * Created by PhpStorm.
 * User: tcdahe
 * Date: 2018/4/10
 * Time: 15:56
 */

namespace Admin\Controller\Tasks;

use Admin\Controller\Tasks\TasksController;
use Admin\Model\ModuleModel;
use Common\Weixin\Weixin;
use Common\Support\UrlGenerator;
use Admin\Model\UserModel;

class TasksInfoController extends TasksController
{

    public function get_tasks_info()
    {
        
        $this->getTask();
        
        return [
            'taskResult' => session('taskResult'),
            'indexResult' => session('indexResult'),
            'taskCount' => session('taskCount'),
        ];
    }
}
