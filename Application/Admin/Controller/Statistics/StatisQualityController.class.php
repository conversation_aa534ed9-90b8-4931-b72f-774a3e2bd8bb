<?php
namespace Admin\Controller\Statistics;

use Admin\Controller\Login\CheckLoginController;
use Admin\Model\AdverseModel;
use Admin\Model\QualityModel;


class StatisQualityController extends CheckLoginController
{
    private $MODULE = 'Statistics';

    public function qualityAnalysis()
    {
        if(IS_POST){
            $qualityModel = new QualityModel();
            $action = I('post.action');
            if($action == 'getLists'){
                $result = $qualityModel->getAllQualities();
                $this->ajaxReturn($result);
            }elseif($action == 'getChartData'){
                $data = $qualityModel->getAllQualities();
                $result = array();
                if($data['code'] != 400){
                    $result = $qualityModel->formatChartData($data,I('post.pic_type'));
                }
                $this->ajaxReturn($result);
            }
        }else{
            $end_date = date('Y-m-d');
            $start_date = date('Y-m-d',strtotime("-6 month"));
            $this->assign('start_date',$start_date);
            $this->assign('end_date',$end_date);
            $this->assign('qualityAnalysis',get_url());
            $this->display();
        }
    }

    public function resultAnalysis()
    {
        $qualityModel = new QualityModel();
        if(IS_POST){
            $action = I('post.action');
            $type = I('post.type');
            $result = [];
            switch ($action){
                case 'getLists':
                    $result = $qualityModel->getDataLists($type);
                    break;
                case 'get_abnormal_detail':
                    $result = $qualityModel->get_abnormal_detail();
                    break;
            }
            $this->ajaxReturn($result);
        }else{
            //查询科室、品牌、供应商
            $departments = $qualityModel->DB_get_all('department','departid,department',array('is_delete'=>0,'hospital_id'=>session('current_hospitalid')));
            $brands = $qualityModel->DB_get_all('dic_brand','brand_id,brand_name',array('is_delete'=>0));
            $suppliers = $qualityModel->DB_get_all('offline_suppliers','olsid,sup_name',array('is_delete'=>0,'is_supplier'=>1));

            //获取模板
            $templates = $qualityModel->DB_get_all('quality_templates', '*', array('1'),'','qtemid asc');
            $end_date = date('Y-m-d');
            $start_date = date('Y-m-d',strtotime("-6 month"));
            $this->assign('start_date',$start_date);
            $this->assign('end_date',$end_date);
            $this->assign('resultAnalysis',get_url());
            $this->assign('templates',$templates);
            $this->assign('departments',$departments);
            $this->assign('brands',$brands);
            $this->assign('suppliers',$suppliers);
            $this->assign('qtemid',$templates[0]['qtemid']);
            $this->display();
        }
    }
}

