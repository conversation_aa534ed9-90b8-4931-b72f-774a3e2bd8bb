<?php
namespace Admin\Controller\Statistics;

use Admin\Controller\Login\CheckLoginController;
use Admin\Model\AdverseModel;
use Admin\Model\CommonModel;
use Admin\Model\QualityModel;
use Admin\Model\RepairModel;


class StatisRepairController extends CheckLoginController
{
    private $MODULE = 'Statistics';

    /**
     * Notes: 维修费用统计
     */
    public function repairFeeStatis()
    {
        $repairModel = new RepairModel();
        if(IS_POST){
            $action = I('post.action');
            if($action == 'getLists'){
                $result = $repairModel->getAllRepairRecord();
                $this->ajaxReturn($result);
            }
        }else{
            $end_date = date('Y-m-d');
            $start_date = date('Y-m-d',strtotime("-6 month"));
            $options = $repairModel->getStatisSearchOption();
            //获取所有符合条件的科室、工程师，维修类型、故障分类
            $this->assign('start_date',$start_date);
            $this->assign('end_date',$end_date);
            $this->assign('repairFeeStatis',get_url());
            $this->assign('departments',$options['departments']);
            $this->assign('repair_type',$options['repair_type']);
            $this->assign('fault_type',$options['fault_type']);
            $this->assign('users',$options['users']);
            $this->display();
        }
    }

    /**
     * Notes: 维修费用分析
     */
    public function repairAnalysis()
    {
        $repairModel = new RepairModel();
        if(IS_POST){
            $action = I('post.action');
            $type = I('post.type');
            $result = [];
            if($action == 'getLists'){
                $result = $repairModel->getDataLists($type);
            }
            $this->ajaxReturn($result);
        }else{
            $end_date = date('Y-m-d');
            $start_date = date('Y-m-d',strtotime("-6 month"));
            $this->assign('start_date',$start_date);
            $this->assign('end_date',$end_date);
            $this->assign('repairAnalysis',get_url());
            $this->display();
        }
    }

    /**
     * Notes: 工程师工作量对比
     */
    public function engineerCompar()
    {
        $repairModel = new RepairModel();
        if(IS_POST){
            $action = I('post.action');
            $result = [];
            if($action == 'getLists'){
                $result = $repairModel->getEngineerJobRecords();
            }
            $this->ajaxReturn($result);
        }else{
            $end_date = date('Y-m-d');
            $start_date = date('Y-m-d',strtotime("-6 month"));
            $this->assign('start_date',$start_date);
            $this->assign('end_date',$end_date);
            $this->assign('engineerCompar',get_url());
            $this->display();
        }
    }

    /**
     * Notes: 工程师评价对比
     */
    public function engineerEva()
    {
        $repairModel = new RepairModel();
        if(IS_POST){
            $action = I('post.action');
            $result = [];
            if($action == 'getLists'){
                $result = $repairModel->getEngineerEvaRecords();
            }
            $this->ajaxReturn($result);
        }else{
            $end_date = date('Y-m-d');
            $start_date = date('Y-m-d',strtotime("-6 month"));
            $this->assign('start_date',$start_date);
            $this->assign('end_date',$end_date);
            $this->assign('engineerEva',get_url());
            $this->display();
        }
    }

    /**
     * Notes: 科室维修费用趋势分析
     */
    public function repairFeeTrend()
    {   

        $repairModel = new RepairModel();
        $hospital_id = session('current_hospitalid');
        if(IS_POST){
            $action = I('post.action');
            $show_type = I('post.show_type');
            $count_type = I('post.count_type');
            switch ($action){
                case 'table':
                    //获取年度每月各科室维修数据
                    $departids = session('departid');
                    if(I('post.departids')){
                        $departids = I('post.departids');
                    }
                    $year = I('post.year');
                    $data = $repairModel->getDepartFreeEachMonths($hospital_id,$year,$departids);
                    $result['code'] = 200;
                    $result['rows'] = $data;
                    if (!$result['rows']) {
                        $result['msg'] = '暂无相关数据';
                        $result['code'] = 400;
                    }
                    $this->ajaxReturn($result);
                    break;
                case 'chart':
                    //获取医院近三年来维修设备数据 次数 工时 费用
                    $departids = session('departid');
                    $current_year = date('Y');
                    $year_data = [];
                    for ($i = 0;$i < 3;$i++){
                        $year_data[$i] = $repairModel->getDepartFreeEachMonths($hospital_id,$current_year - $i,$departids);
                    }
                    //组织近三年数据
                    $result = $this->formatYearData($year_data,$count_type,$show_type);
                    $this->ajaxReturn($result);
                    break;
            }
        }else{
            //获取科室
            $departments = $repairModel->get_departments_by_hospital($hospital_id);
            $this->assign('departments',$departments);
            $this->assign('year',date('Y'));
            $this->assign('repairFeeTrend',get_url());
            $this->display();
        }
    }

    private function formatYearData($year_data,$count_type,$show_type)
    {
        $current_year = date('Y');
        $option = [];
        if(!$count_type){
            return false;
        }
        for ($i = 0;$i < 3;$i++){
            $option['legend'][] = (string)($current_year - $i);
            if($show_type == 'bar'){
                $option['tooltip']['axisPointer']['type'] = 'shadow';
            }else{
                $option['tooltip']['axisPointer']['type'] = 'line';
            }
            $option['series'][$i]['name'] = $current_year - $i;
            $option['series'][$i]['type'] = $show_type;
            //$option['series'][$i]['stack'] = '总量';
            for ($k = 0;$k < 12;$k++){
                $option['xAxis']['data'][$k] = ($k+1).' 月';
                $option['series'][$i]['data'][$k] = 0;
                $option['series'][$i]['smooth'][$k] = true;//圆滑曲线
            }
        }
        switch ($count_type){
            case 'times':
                $option['title'] = '维修次数';
                $option['yAxis']['name'] = '单位：次';
                foreach ($year_data as $k=>$value){
                    foreach ($value as $k1=>$v1){
                        for($i = 0;$i < 12;$i++){
                            $option['series'][$k]['data'][$i] += $v1['repair_num_'.($i+1)];
                        }
                    }
                }
                break;
            case 'hours':
                $option['title'] = '维修工时';
                $option['yAxis']['name'] = '单位：时';
                foreach ($year_data as $k=>$value){
                    foreach ($value as $k1=>$v1){
                        for($i = 0;$i < 12;$i++){
                            $option['series'][$k]['data'][$i] += $v1['repair_hours_'.($i+1)];
                        }
                    }
                }
                break;
            case 'free':
                $option['title'] = '维修费用';
                $option['yAxis']['name'] = '单位：元';
                foreach ($year_data as $k=>$value){
                    foreach ($value as $k1=>$v1){
                        for($i = 0;$i < 12;$i++){
                            $option['series'][$k]['data'][$i] += $v1['repair_price_'.($i+1)];
                        }
                    }
                }
                break;
        }
        return $option;
    }
}

