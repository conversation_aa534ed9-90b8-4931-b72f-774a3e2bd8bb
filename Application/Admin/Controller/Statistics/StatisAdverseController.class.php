<?php
namespace Admin\Controller\Statistics;

use Admin\Controller\Login\CheckLoginController;
use Admin\Model\AdverseModel;


class StatisAdverseController extends CheckLoginController
{
    private $MODULE = 'Statistics';

    public function adverseAnalysis()
    {
        if(IS_POST){
            $adverModel = new AdverseModel();
            $action = I('post.action');
            if($action == 'getLists'){
                $result = $adverModel->getAllAdverses();
                $this->ajaxReturn($result);
            }elseif($action == 'getChartData'){
                $data = $adverModel->getAllAdverses();
                $result = array();
                if($data['code'] != 400){
                    $result = $adverModel->formatChartData($data,I('post.pic_type'));
                }
                $this->ajaxReturn($result);
            }
        }else{
            $end_date = date('Y-m-d');
            $start_date = date('Y-m-d',strtotime("-6 month"));
            $this->assign('start_date',$start_date);
            $this->assign('end_date',$end_date);
            $this->assign('adverseAnalysis',get_url());
            $this->display();
        }
    }
}

