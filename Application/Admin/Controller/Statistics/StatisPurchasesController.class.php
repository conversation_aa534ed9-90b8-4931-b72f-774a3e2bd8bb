<?php
/**
 * Created by PhpStorm.
 * User: 邓锦龙
 * Date: 2018/10/19
 * Time: 10:50
 */

namespace Admin\Controller\Statistics;;


use Admin\Controller\Login\CheckLoginController;
use Admin\Model\DepartmentModel;
use Admin\Model\PurchasesStatisModel;

class StatisPurchasesController extends CheckLoginController
{
    private $MODULE = 'Statistics';

    /**
     * Notes: 采购费用统计
     */
    public function purFeeStatis()
    {
        $purModel = new PurchasesStatisModel();
        if(IS_POST){
            $action = I('post.action');
            if($action == 'getLists'){
                $result = $purModel->getAllContractedAssets();
                $this->ajaxReturn($result);
            }
        }else{
            $hospital_id = session('current_hospitalid');
            $departModel = new DepartmentModel();
            $departments = $departModel->DB_get_all('department', 'departid,department', array('is_delete' => C('NO_STATUS'), 'hospital_id' => $hospital_id));
            $this->assign('departments', $departments);
            $this->assign('year', date('Y'));
            $this->assign('purFeeStatis',get_url());
            $this->display();
        }
    }

    /**
     * Notes: 采购费用分析
     */
    public function purAnalysis()
    {
        $purModel = new PurchasesStatisModel();
        if(IS_POST){
            $action = I('post.action');
            $type = I('post.type');
            $result = [];
            if($action == 'getLists'){
                $result = $purModel->getDataLists($type);
            }
            $this->ajaxReturn($result);
        }else{
            $this->assign('year',date('Y'));
            $this->assign('purAnalysis',get_url());
            $this->display();
        }
    }

}