<?php

namespace Admin\Model;

class TasksModel extends CommonModel
{
    protected $len = 100;
    protected $tableName = 'repair';

        /**
     * Notes: 获取附属设备待验收
     */
    public function get_my_subsidiary_examines($tasks)
    {
        //附属设备待验收
        $where = [];
        $where['status'] = array('EQ', '1');
        // 查询超过5天没验收的
        $currentDate = date('Y-m-d H:i:s', strtotime('-5 days'));
        $where['approve_time'] = array('LT', $currentDate);

        $tasks['subsidiary_examines']['nums'] = $this->DB_get_count('subsidiary_allot', $where);
        $tasks['subsidiary_examines']['name'] = '附属设备分配待验收';
        
        return $tasks;
    }

    /**
     * Notes: 获取待验收的维修任务列表和数量
     */
    public function get_my_repair_examines($tasks)
    {
        //查询当前用户是否有验收权限
        $where = [];
        $where['status'] = array('EQ', C('REPAIR_MAINTENANCE_COMPLETION'));
        // 查询超过5天没验收的
        $currentDate = date('Y-m-d H:i:s', strtotime('-5 days'));
        $where['overdate'] = array('LT', $currentDate);

        $tasks['repair_examines']['nums'] = $this->DB_get_count('repair', $where);
        $tasks['repair_examines']['name'] = '维修待验收';
        
        return $tasks;
    }

    /**
     * Notes: 获取巡查保养完成待验收
     */
    public function get_my_patrol_examines($tasks)
    {
        $where = [];
        $where['cycle_status'] = ['IN', '2,3'];
        $where['check_status'] = 0;
        
        // 查询超过5天没验收的
        $currentDate = date('Y-m-d H:i:s', strtotime('-5 days'));
        $where['complete_time'] = array('LT', $currentDate);
        
        $tasks['patrol_examines']['nums'] = $this->DB_get_count('patrol_plans_cycle', $where);
        $tasks['patrol_examines']['name'] = '保养完成待验收';

        return $tasks;
    }

    /**
     * Notes: 获取待验收的转科任务
     */
    public function get_my_transfer_examines($tasks)
    {
        $where = [];
        $where['is_check'] = array('EQ', C('TRANSFER_IS_NOTCHECK'));
        $where['approve_status'][0] = 'IN';
        $where['approve_status'][1][] = C('STATUS_APPROE_UNWANTED');
        $where['approve_status'][1][] = C('STATUS_APPROE_SUCCESS');
        
        // 查询超过5天没验收的
        $currentDate = date('Y-m-d H:i:s', strtotime('-5 days'));
        $where['approve_time'] = array('LT', $currentDate);
        
        $tasks['transfer_examines']['nums'] = $this->DB_get_count('assets_transfer', $where);
        $tasks['transfer_examines']['name'] = '转科验收';
        
        return $tasks;
    }

    /**
     * Notes: 获取借调待验收任务
     */
    public function get_my_assets_borrow_examines($tasks)
    {
        $where = [];
        $where['status'] = array('EQ', C('BORROW_STATUS_BORROW_IN'));
        
        // 查询超过5天没验收的
        $currentDate = date('Y-m-d H:i:s', strtotime('-5 days'));
        $where['apply_time'] = array('LT', strtotime($currentDate));

        $tasks['assets_borrow']['nums'] = $this->DB_get_count('assets_borrow', $where);
        $tasks['assets_borrow']['name'] = '归还验收';

        return $tasks;
    }

    /**
     * Notes: 获取外调待验单待录入
     */
    public function get_my_assets_outside_examines($tasks)
    {
        $where = [];
        $where['status'] = array('EQ', C('OUTSIDE_STATUS_ACCEPTANCE_CHECK'));
        
        // 查询超过5天没验收的
        $currentDate = date('Y-m-d H:i:s', strtotime('-5 days'));
        $where['approve_time'] = array('LT', $currentDate);

        $tasks['assets_outside_examines']['nums'] = $this->DB_get_count('assets_outside', $where);
        $tasks['assets_outside_examines']['name'] = '外调待验单待录入';
        
        return $tasks;
    }

}
