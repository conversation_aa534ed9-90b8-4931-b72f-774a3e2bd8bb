<?php
/**
 * Created by PhpStorm.
 * User: 邓锦龙
 * Date: 2018/10/19
 * Time: 10:53
 */

namespace Admin\Model;


use Think\Model;

class PurchasesStatisModel extends CommonModel
{
    private $MODULE = 'Purchases';
    private $Controller = 'PurchasePlans';
    protected $tablePrefix = 'sb_';
    protected $tableName = 'purchases_depart_apply_assets';

    public function getAllContractedAssets()
    {
        $limit = I('post.limit') ? I('post.limit') : 10;
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $order = I('post.order') ? I('post.order') : 'desc';
        $sort = I('post.sort') ? I('post.sort') : 'departid';
        $departids = I('post.departids');
        $year = I('post.year') ? I('post.year') : date('Y');
        $where['hospital_id'] = session('current_hospitalid');
        $where['contract_id'] = array('exp','is not null');
        if($departids){
            $where['departid'] = array('in',$departids);
        }
        $where['apply_date'] = array(array('egt',$year.'-01-01'),array('elt',$year.'-12-31'),'and');
        $fields = "departid,SUM(nums) AS nums,
        SUM(nums*buy_price) as total_price,
        group_concat(distinct apply_id) as apply_times,
        SUM(if(is_import = '1',1,0)*nums) as is_import_1,
        SUM(if(is_import = '0',1,0)*nums) as is_import_2,
        SUM(if(buy_type = '1',1,0)*nums) as buy_type_1,
        SUM(if(buy_type = '2',1,0)*nums) as buy_type_2,
        SUM(if(buy_type = '3',1,0)*nums) as buy_type_3";
        $count = $this->DB_get_all('purchases_depart_apply_assets','departid',$where,'departid');
        $data = $this->DB_get_all('purchases_depart_apply_assets',$fields,$where,'departid',$sort . ' ' . $order, '');
        $departname = array();
        include APP_PATH . "Common/cache/department.cache.php";
        foreach ($data as &$v){
            $v['apply_type_1'] = $v['apply_type_2'] = 0;
            $applyids = explode(',',$v['apply_times']);
            //获取计划内个数
            $nei = $this->DB_get_count('purchases_depart_apply',array('apply_type'=>1,'apply_id'=>array('in',$applyids)));
            $v['apply_type_1'] = (int)$nei;
            //总申请次数
            $v['apply_times'] = count($applyids);
            //总申请次数 - 计划内个数 == 计划外
            $v['apply_type_2'] = $v['apply_times'] - $nei;
            $v['department']  = $departname[$v['departid']]['department'];
            $v['year']  = $year;
        }
        $result['limit'] = (int)$limit;
        $result['offset'] = $offset;
        $result['total'] = (int)count($count);
        $result['rows'] = $data;
        $result['code'] = 200;
        if (!$result['rows']) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
        }
        return $result;
    }

    /**
     * Notes: 获取采购费用分析数据
     * @param $type string 统计类型
     */
    public function getDataLists($type)
    {
        $result = [];
        switch ($type){
            case 'assetsNums':
                $result = $this->getDepartmentAssetsNums();
                break;
            case 'assetsFee':
                $result = $this->getDepartmentAssetsFee();
                break;
            case 'buyType':
                $result = $this->getAssetsBuyType();
                break;
        }
        return $result;
    }

    /**
     * Notes: 科室采购数据统计
     * @return mixed
     */
    public function getDepartmentAssetsNums()
    {
        $year = I('post.year') ? I('post.year') : date('Y');
        $order = I('POST.order') ? I('POST.order') : 'DESC';
        $sort = I('POST.sort') ? I('POST.sort') : 'departid';
        $limit = I('post.limit') ? I('post.limit') : C('DEFAULT_LIMIT');
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $where['hospital_id'] = session('current_hospitalid');
        $where['contract_id'] = array('exp','is not null');
        $where['apply_date'] = array(array('egt',$year.'-01-01'),array('elt',$year.'-12-31'),'and');

        $fields = "departid,SUM(nums) AS nums";
        $count = $this->DB_get_all('purchases_depart_apply_assets',$fields,$where,'departid');
        $data = $this->DB_get_all('purchases_depart_apply_assets',$fields,$where,'departid',$sort . ' ' . $order, $offset . "," . $limit);
        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        $chartData = [];
        $total_nums = 0;
        foreach ($data as $k=>$v){
            $total_nums += $v['nums'];
        }
        foreach ($data as $k=>$v){
            $data[$k]['department'] = $departname[$v['departid']]['department'];
            $data[$k]['num_ratio'] = number_format($v['nums']/$total_nums*100,2).'%';
        }
        //组织图表数据
        foreach ($data as $k=>$v){
            $chartData['legend_data'][$k] = $v['department'];
            $chartData['series_data'][$k]['value'] = $v['nums'];
            $chartData['series_data'][$k]['name'] = $v['department'];
        }
        $result['total'] = count($count);
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["code"] = 200;
        $result['rows'] = $data;
        $result['charData'] = $chartData;
        if(!$result['rows']){
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
        }
        return $result;
    }

    /**
     * Notes: 科室采购设备费用统计
     * @return mixed
     */
    public function getDepartmentAssetsFee()
    {
        $year = I('post.year') ? I('post.year') : date('Y');
        $order = I('POST.order') ? I('POST.order') : 'DESC';
        $sort = I('POST.sort') ? I('POST.sort') : 'departid';
        $limit = I('post.limit') ? I('post.limit') : C('DEFAULT_LIMIT');
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $where['hospital_id'] = session('current_hospitalid');
        $where['contract_id'] = array('exp','is not null');
        $where['apply_date'] = array(array('egt',$year.'-01-01'),array('elt',$year.'-12-31'),'and');

        $fields = "departid,SUM(nums*buy_price) AS total_price";
        $count = $this->DB_get_all('purchases_depart_apply_assets',$fields,$where,'departid');
        $data = $this->DB_get_all('purchases_depart_apply_assets',$fields,$where,'departid',$sort . ' ' . $order, $offset . "," . $limit);
        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        $chartData = [];
        $totalP = 0;
        foreach ($data as $k=>$v){
            $totalP += $v['total_price'];
        }
        foreach ($data as $k=>$v){
            $data[$k]['department'] = $departname[$v['departid']]['department'];
            $data[$k]['fee_ratio'] = number_format($v['total_price']/$totalP*100,2).'%';
        }
        //组织图表数据
        foreach ($data as $k=>$v){
            $chartData['legend_data'][$k] = $v['department'];
            $chartData['series_data'][$k]['value'] = $v['total_price'];
            $chartData['series_data'][$k]['name'] = $v['department'];
        }
        $result['total'] = count($count);
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["code"] = 200;
        $result['rows'] = $data;
        $result['charData'] = $chartData;
        if(!$result['rows']){
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
        }
        return $result;
    }

    /**
     * Notes: 科室采购设购置类型统计
     * @return mixed
     */
    public function getAssetsBuyType()
    {
        $year = I('post.year') ? I('post.year') : date('Y');
        $order = I('POST.order') ? I('POST.order') : 'DESC';
        $sort = I('POST.sort') ? I('POST.sort') : 'departid';
        $limit = I('post.limit') ? I('post.limit') : C('DEFAULT_LIMIT');
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $where['hospital_id'] = session('current_hospitalid');
        $where['contract_id'] = array('exp','is not null');
        $where['apply_date'] = array(array('egt',$year.'-01-01'),array('elt',$year.'-12-31'),'and');

        $fields = "departid,
        SUM(if(buy_type = '1',1,0)*nums) AS buy_type_1, 
        SUM(if(buy_type = '2',1,0)*nums) AS buy_type_2,
        SUM(if(buy_type = '3',1,0)*nums) AS buy_type_3";
        $data = $this->DB_get_all('purchases_depart_apply_assets',$fields,$where,'departid',$sort . ' ' . $order, $offset . "," . $limit);
        if(!$data){
            $result['total'] = 0;
            $result["offset"] = $offset;
            $result["limit"] = $limit;
            $result["code"] = 200;
            $result['rows'] = array();
            $result['charData'] = array();
            if(!$result['rows']){
                $result['msg'] = '暂无相关数据';
                $result['code'] = 400;
            }
            return $result;
        }
        //统计费用
        $where_free['hospital_id'] = session('current_hospitalid');
        $where_free['contract_id'] = array('exp','is not null');
        $where_free['apply_date'] = array(array('egt',$year.'-01-01'),array('elt',$year.'-12-31'),'and');

        $fields = "buy_type,SUM(nums*buy_price) as total_price";
        $free_data = $this->DB_get_all('purchases_depart_apply_assets',$fields,$where_free,'buy_type','buy_type asc');
        $res = $chartData = [];
        $total_buy_type_1 = $total_buy_type_2 = $total_buy_type_3 = 0;
        foreach ($data as $k=>$v){
            $total_buy_type_1 += $v['buy_type_1'];
            $total_buy_type_2 += $v['buy_type_2'];
            $total_buy_type_3 += $v['buy_type_3'];
        }
        $res[0]['buy_type_nums'] = $total_buy_type_1;
        $res[0]['buy_type_name'] = '报废更新';
        $res[0]['buy_type_price'] = 0;
        foreach ($free_data as $k=>$v){
            if($v['buy_type'] == 1){
                $res[0]['buy_type_price'] = $free_data[$k]['total_price'];
            }
        }

        $res[1]['buy_type_nums'] = $total_buy_type_2;
        $res[1]['buy_type_name'] = '添置';
        $res[1]['buy_type_price'] = 0;
        foreach ($free_data as $k=>$v){
            if($v['buy_type'] == 2){
                $res[1]['buy_type_price'] = $free_data[$k]['total_price'];
            }
        }

        $res[2]['buy_type_nums'] = $total_buy_type_3;
        $res[2]['buy_type_name'] = '新增';
        $res[2]['buy_type_price'] = 0;
        foreach ($free_data as $k=>$v){
            if($v['buy_type'] == 3){
                $res[2]['buy_type_price'] = $free_data[$k]['total_price'];
            }
        }

        $totalfree = $free_data[0]['total_price'] + $free_data[1]['total_price'] + $free_data[2]['total_price'];
        foreach ($res as $k=>$v){
            $res[$k]['num_ratio'] = number_format($v['buy_type_nums']/($total_buy_type_1+$total_buy_type_2+$total_buy_type_3)*100,2).'%';
            $res[$k]['price_ratio'] = number_format($v['buy_type_price']/($totalfree)*100,2).'%';
        }
        //组织图表数据
        foreach ($res as $k=>$v){
            $chartData['legend_data'][$k] = $v['buy_type_name'];
            $chartData['series_data'][$k]['value'] = $v['buy_type_price'];
            $chartData['series_data'][$k]['name'] = $v['buy_type_name'];
        }
        $result['total'] = count($res);
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["code"] = 200;
        $result['rows'] = $res;
        $result['charData'] = $chartData;
        if(!$result['rows']){
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
        }
        return $result;
    }
}