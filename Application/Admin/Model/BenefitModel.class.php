<?php
/**
 * Created by PhpStorm.
 * User: 邓锦龙
 * Date: 2017/7/25
 * Time: 9:44
 */

namespace Admin\Model;

use Think\Model;
use Think\Model\RelationModel;

class BenefitModel extends CommonModel
{
    protected $tablePrefix = 'sb_';
    protected $MODULE = 'Benefit';
    protected $CONTROLLER = 'Benefit';
    protected $len = 100;
    protected $tableName = 'assets_info';


    // 设备收支录入
    public function assetsBenefitList()
    {
        $departid = session('departid');
        $order = I('POST.order') ? I('POST.order') : 'desc';
        $sort = I('POST.sort') ? I('POST.sort') : 'assid';
        $hospital_id = I('POST.hospital_id');
        $assets = I('POST.assetsName');
        $assetsDep = I('POST.assetsDep');
        $Date = I('POST.assetsBenefitListDate');
        $limit = I('post.limit') ? I('post.limit') : C('DEFAULT_LIMIT');
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $where['status'] = array('NEQ', C('ASSETS_STATUS_SCRAP'));//排除已报废设备
        $where['is_benefit'] = array('EQ', C('YES_STATUS'));//设备类型为效益分析的设备
        $where['main_assid'] = ['EQ', C('NO_STATUS')];//排除附属设备
        if (!$departid) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        //设备名称搜索
        if ($assets) {
            $where['assets'] = array('LIKE', '%' . $assets . '%');
        }
        if (!$Date) {
            $Date = date('Y-m', time());
        }
        if ($hospital_id) {
            $where['hospital_id'] = $hospital_id;
        } else {
            $where['hospital_id'] = session('current_hospitalid');
        }

        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        //部门搜索
        if ($assetsDep) {
            $where['departid'][] = array('in', $assetsDep);
        } else {
            $where['departid'][] = array('IN', $departid);
        }

        $total = $this->DB_get_count('assets_info', $where);
        $fileds = 'assid,assets,catid,assnum,model,departid';
        $asArr = $this->DB_get_all('assets_info', $fileds, $where, '', $sort . ' ' . $order, $offset . "," . $limit);
        if (!$asArr) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        $catname = [];
        include APP_PATH . "Common/cache/category.cache.php";
        $assnumArr = [];
        foreach ($asArr as &$one) {
            $assnumArr [] = $one['assnum'];
            $one['category'] = $catname[$one['catid']]['category'];
        }
        //获取对应明细数据
        $fileds = 'benefitid,assnum,departid,entryDate,income,work_number,depreciation_cost,material_cost,maintenance_cost,
        management_cost,interest_cost,comprehensive_cost,operator,work_day,positive_rate';
        $BenefitWhere['assnum'] = array('IN', $assnumArr);
        $BenefitWhere['entryDate'] = array('EQ', $Date);
        $BenefitData = $this->DB_get_all('assets_benefit', $fileds, $BenefitWhere);
        $BenefitRes = [];
        foreach ($BenefitData as &$BenefitValue) {
            //生成用对用唯一值做键值得数组
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['benefitid'] = $BenefitValue['benefitid'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['income'] = $BenefitValue['income'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['departid'] = $BenefitValue['departid'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['entryDate'] = $BenefitValue['entryDate'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['work_number'] = $BenefitValue['work_number'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['depreciation_cost'] = $BenefitValue['depreciation_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['material_cost'] = $BenefitValue['material_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['maintenance_cost'] = $BenefitValue['maintenance_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['management_cost'] = $BenefitValue['management_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['operator'] = $BenefitValue['operator'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['comprehensive_cost'] = $BenefitValue['comprehensive_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['interest_cost'] = $BenefitValue['interest_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['work_day'] = $BenefitValue['work_day'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['positive_rate'] = $BenefitValue['positive_rate'];
        }
        foreach ($asArr as &$one) {
//            var_dump($BenefitRes[$one['assnum'].$Date]);
            $one['income'] = $BenefitRes[$one['assnum'] . $Date]['income'];
            $one['entryDate'] = $Date;
            $one['work_number'] = $BenefitRes[$one['assnum'] . $Date]['work_number'];
            $one['depreciation_cost'] = $BenefitRes[$one['assnum'] . $Date]['depreciation_cost'];
            $one['material_cost'] = $BenefitRes[$one['assnum'] . $Date]['material_cost'];
            $one['maintenance_cost'] = $BenefitRes[$one['assnum'] . $Date]['maintenance_cost'];
            $one['management_cost'] = $BenefitRes[$one['assnum'] . $Date]['management_cost'];
            $one['operator'] = $BenefitRes[$one['assnum'] . $Date]['operator'];
            $one['comprehensive_cost'] = $BenefitRes[$one['assnum'] . $Date]['comprehensive_cost'];
            $one['interest_cost'] = $BenefitRes[$one['assnum'] . $Date]['interest_cost'];
            $one['work_day'] = $BenefitRes[$one['assnum'] . $Date]['work_day'];
            if ($BenefitRes[$one['assnum'] . $Date]['departid']) {
                $one['benefitid'] = $BenefitRes[$one['assnum'] . $Date]['benefitid'];
                $one['positive_rate'] = $BenefitRes[$one['assnum'] . $Date]['positive_rate'];
                $one['department'] = $departname[$BenefitRes[$one['assnum'] . $Date]['departid']]['department'];
                //录入成功的 计算结余
                $one['balance'] = $one['income'] - $one['depreciation_cost'] - $one['material_cost'] - $one['maintenance_cost'] - $one['management_cost'] - $one['comprehensive_cost'] - $one['interest_cost'];
                $one['balance'] = number_format($one['balance'], '2', '.', '');
                $one['surplus_rate'] = $one['income'] > 0 ? number_format($one['balance'] / $one['income'], 4, '.', '') * 100 : 0;
            } else {
                $one['department'] = $departname[$one['departid']]['department'];
            }
        }
        $result["total"] = $total;
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["code"] = 200;
        $result["rows"] = $asArr;
        return $result;
    }

    // 单机效益分析
    public function singleBenefitList()
    {
        $departid = session('departid');
        $order = I('POST.order');
        $hospital_id = I('POST.hospital_id');
        $sort = I('POST.sort');
        $assets = I('POST.assetsName');
        $assetsDep = I('POST.assetsDep');
        $startDate = I('POST.startDate');
        $endDate = I('POST.endDate');
        $limit = I('post.limit') ? I('post.limit') : C('DEFAULT_LIMIT');
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        //$where['status'] = array('NEQ', C('ASSETS_STATUS_SCRAP'));
        //有效益分析的设备
        $where['is_benefit'] = array('EQ', C('YES_STATUS'));
        if ($hospital_id) {
            $where['hospital_id'] = $hospital_id;
        } else {
            $where['hospital_id'] = session('current_hospitalid');
        }
        if ($departid) {
            $where['departid'][] = array('IN', $departid);
        } else {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        if (!$order) {
            $order = 'desc';
        }
        //用比较小的作为开始月份
        if ($startDate > $endDate) {
            $temporary = $startDate;
            $startDate = $endDate;
            $endDate = $temporary;
        }
        if (!$startDate && !$endDate) {
            $startDate = $endDate = date('Y-m', time());
        }
        //设备名称搜索
        if ($assets) {
            $where['assets'] = array('LIKE', '%' . $assets . '%');
        }
        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        //部门搜索
        if ($assetsDep != '') {
            foreach ($departname as &$one) {
                if (array_keys($one, $assetsDep)) {
                    $assetsDep = array_keys($departname, $one)[0];
                }
            }
            $where['departid'][] = array('EQ', $assetsDep);
        }
        $sort ?: $sort = 'assid';
        $total = $this->DB_get_count('assets_info', $where);
        $fileds = 'assid,assets,catid,assnum,model,departid';
        $asArr = $this->DB_get_all('assets_info', $fileds, $where, '', $sort . ' ' . $order, $offset . "," . $limit);
        if (!$asArr) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        $catname = [];
        include APP_PATH . "Common/cache/category.cache.php";
        $assnumArr = [];
        foreach ($asArr as &$one) {
            $assnumArr [] = $one['assnum'];
        }
        //获取对应明细数据
        $fileds = 'assnum,entryDate,income,work_number,depreciation_cost,material_cost,maintenance_cost,
        management_cost,interest_cost,comprehensive_cost';
        $BenefitWhere['assnum'] = array('IN', $assnumArr);
        $BenefitWhere['entryDate'][] = array('EGT', $startDate);
        $BenefitWhere['entryDate'][] = array('ELT', $endDate);
        $monthNum = $this->getMonthNum($startDate, $endDate);
        $BenefitData = $this->DB_get_all('assets_benefit', $fileds, $BenefitWhere);
        $BenefitRes = [];

        $menu = get_menu('Benefit', 'Benefit', 'singleBenefitList');

        foreach ($BenefitData as &$BenefitValue) {
            //生成用对用唯一值做键值(设备编号+日期)得数组
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['income'] = $BenefitValue['income'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['all_cost'] = +$BenefitValue['depreciation_cost']
                + $BenefitValue['material_cost'] + $BenefitValue['maintenance_cost'] + $BenefitValue['management_cost']
                + $BenefitValue['comprehensive_cost'] + $BenefitValue['interest_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['departid'] = $BenefitValue['departid'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['work_number'] = $BenefitValue['work_number'];
        }
        foreach ($asArr as &$one) {
            $one['income'] = 0;//总收入
            $one['all_cost'] = 0;//总支出
            $one['work_number'] = 0;//总诊疗次数
            $income = $all_cost = $work_number = 0;
            for ($i = 0; $i < $monthNum; $i++) {
                //循环月份统计的次数
                $ym = date('Y-m', strtotime("$startDate +$i month"));
                $income += $BenefitRes[$one['assnum'] . $ym]['income'];
                $all_cost += $BenefitRes[$one['assnum'] . $ym]['all_cost'];
                $work_number += $BenefitRes[$one['assnum'] . $ym]['work_number'];
            }
            $one['income'] = number_format($income, '2', '.', '');
            $one['all_cost'] = number_format($all_cost, '2', '.', '');
            $one['work_number'] = $work_number;

            $balance = number_format($one['income'] - $one['all_cost'], '2', '.', '');
            $one['balance'] = $balance < 0 ? '<span class="rquireCoin">' . $balance . '</span>' : $balance;

            $one['category'] = $catname[$one['catid']]['category'];
            $one['department'] = $departname[$one['departid']]['department'];
            $one['startDate'] = $startDate;
            $one['endDate'] = $endDate;
            $one['monthNum'] = $monthNum;
            if ($menu) {
                $one['operation'] = $this->returnListLink('查看', $this->full_open_url($this->MODULE, $this->CONTROLLER) . 'assetsBenefitData', 'see', C('BTN_CURRENCY'));
            }
        }
        $result["total"] = $total;
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["code"] = 200;
        $result["rows"] = $asArr;
        return $result;
    }

    // 科室效益分析
    public function departmentBenefitList()
    {
        $departid = session('departid');
        $hospital_id = I('POST.hospital_id');
        $order = I('POST.order') ? I('POST.order') : 'desc';
        $sort = I('POST.sort') ? I('POST.sort') : 'departid';
        $assetsDep = I('POST.assetsDep');
        $startDate = I('POST.departStartDate');
        $endDate = I('POST.departEndDate');
        $limit = I('post.limit') ? I('post.limit') : C('DEFAULT_LIMIT');
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $where['is_delete'] = C('NO_STATUS');
        if ($departid) {
            $where['departid'][] = array('IN', $departid);
        } else {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        if (!$order) {
            $order = 'desc';
        }
        //用比较小的作为开始月份
        if ($startDate > $endDate) {
            $temporary = $startDate;
            $startDate = $endDate;
            $endDate = $temporary;
        }
        if (!$startDate && !$endDate) {
            $startDate = $endDate = date('Y-m', time());
        }
        //部门搜索
        if ($assetsDep) {
            $where['department'] = array('LIKE', '%' . $assetsDep . '%');
        }
        if ($hospital_id) {
            $where['hospital_id'] = $hospital_id;
        } else {
            $where['hospital_id'] = session('current_hospitalid');
        }
        $total = $this->DB_get_count('department', $where);
        $fileds = 'department,departid';
        $deArr = $this->DB_get_all('department', $fileds, $where, '', $sort . ' ' . $order, $offset . "," . $limit);
        if (!$deArr) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        $departidArr = [];
        foreach ($deArr as &$one) {
            $departidArr [] = $one['departid'];
        }
        //获取对应明细数据
        $fileds = 'departid,entryDate,income,work_number,depreciation_cost,material_cost,maintenance_cost,
        management_cost,interest_cost,comprehensive_cost';
        $BenefitWhere['departid'] = array('IN', $departidArr);
        $BenefitWhere['entryDate'][] = array('EGT', $startDate);
        $BenefitWhere['entryDate'][] = array('ELT', $endDate);
        $monthNum = $this->getMonthNum($startDate, $endDate);
        $BenefitData = $this->DB_get_all('assets_benefit', $fileds, $BenefitWhere);
        $BenefitRes = [];
        $menu = get_menu('Benefit', 'Benefit', 'departmentBenefitList');
        foreach ($BenefitData as &$BenefitValue) {
            //生成用对用唯一值做键值(设备编号+日期)得数组
            if (!$BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['departid']) {
                //不存在departid就加入数值
                $BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['departid'] = $BenefitValue['departid'];
                $BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['income'] = $BenefitValue['income'];
                $BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['all_cost'] = $BenefitValue['depreciation_cost']
                    + $BenefitValue['material_cost'] + $BenefitValue['maintenance_cost'] + $BenefitValue['management_cost']
                    + $BenefitValue['comprehensive_cost'] + $BenefitValue['interest_cost'];
                $BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['work_number'] = $BenefitValue['work_number'];
            } else {
                //如果存在departid则最加数值
                $BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['income'] += $BenefitValue['income'];
                $BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['all_cost'] += $BenefitValue['depreciation_cost']
                    + $BenefitValue['material_cost'] + $BenefitValue['maintenance_cost'] + $BenefitValue['management_cost']
                    + $BenefitValue['comprehensive_cost'] + $BenefitValue['interest_cost'];
                $BenefitRes[$BenefitValue['departid'] . $BenefitValue['entryDate']]['work_number'] += $BenefitValue['work_number'];
            }
        }
        foreach ($deArr as &$one) {
            $data = $startDate;
            $one['income'] = 0;
            $one['all_cost'] = 0;
            $one['work_number'] = 0;
            for ($i = 0; $i < $monthNum; $i++) {
                //循环月份统计的次数
                $income = $BenefitRes[$one['departid'] . $data]['income'];
                $all_cost = $BenefitRes[$one['departid'] . $data]['all_cost'];
                $one['income'] = number_format($income, '2', '.', '');
                $one['all_cost'] = number_format($all_cost, '2', '.', '');
                $one['work_number'] += $BenefitRes[$one['departid'] . $data]['work_number'];
                $data = date("Y-m", strtotime($data . "+1 month"));
            }
            $balance = $one['income'] - $one['all_cost'];
            $balance = number_format($balance, '2', '.', '');
            $one['balance'] = $balance < 0 ? '<span class="rquireCoin">' . $balance . '</span>' : $balance;
            $one['startDate'] = $startDate;
            $one['endDate'] = $endDate;
            $one['monthNum'] = $monthNum;
            if ($menu) {
                $one['operation'] = $this->returnListLink('查看', $this->full_open_url($this->MODULE, $this->CONTROLLER) . 'departmentBenefitData', 'see', C('BTN_CURRENCY'));
            }
        }
        $result["total"] = $total;
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["code"] = 200;
        $result["rows"] = $deArr;
        return $result;
    }

    //单机效益分析 饼图
    public function assetsBenefitDataGetPie()
    {
        $startDate = I('POST.startDate');
        $endDate = I('POST.endDate');
        $assnum = I('POST.assnum');
        if ($startDate > $endDate) {
            $temporary = $startDate;
            $startDate = $endDate;
            $endDate = $temporary;
        }
        $where['assnum'] = array('EQ', $assnum);
        $where['entryDate'][] = array('EGT', $startDate);
        $where['entryDate'][] = array('ELT', $endDate);
        //设备收入明细
        $data = $this->DB_get_all('assets_benefit', '', $where);
        //设备明细
        $assArr = $this->DB_get_one('assets_info', 'buy_price', array('assnum' => $assnum));
        $d1 = strtotime($startDate);
        $d2 = strtotime(date("Y-m", strtotime($endDate . "+1 month")));
        //计算总共有多少天
        $Days = round(($d2 - $d1) / 3600 / 24);
        //获取所选范围有多少个月份
        $monthNum = $this->getMonthNum($startDate, $endDate, '-');
        $row = [];
        foreach ($data as &$one) {
            $row['positive_num'] += $one['positive_rate'];
            $row['work_num'] += $one['work_number'];
            $row['workDay'] += $one['work_day'];
            $row['income'] = number_format($row['income'] += $one['income'], 2, '.', '');
            $row['cost'] += $one['depreciation_cost'] + $one['material_cost'] + $one['maintenance_cost'] + $one['management_cost'] + $one['comprehensive_cost'] + $one['interest_cost'];
            $row['cost'] = number_format($row['cost'], '2', '.', '');
        }


        //利润率
        $row['profit_color'] = '#3FA7DC';//默认蓝色
        $row['profit'] = number_format($row['income'] - $row['cost'], 2, '.', '');//利润
        if ($row['income'] > 0) {
            $row['profit_title'] = '利润率' . (number_format($row['profit'] / $row['income'], 4, '.', '') * 100) . '%';
        } else {
            if ($row['profit'] < 0) {
                $row['profit_title'] = '亏损' . $row['profit'] . '元';
            } else {
                $row['profit_title'] = '利润率0%';
            }
        }
        if ($row['profit'] < 0) {
            $row['profit_color'] = '#FF3030';//亏损红色
        }
        //使用率
        $row['downDay'] = $Days - $row['workDay'];//停机天数
        $row['workDay_title'] = '使用率' . (number_format($row['workDay'] / $Days, 4, '.', '') * 100) . '%';
        //回报率
        $row['average_profit_color'] = '#3CB371';//默认绿色
        $row['average_profit'] = number_format($row['profit'] / $monthNum, 2, '.', '');//平均利润
        $row['buy_price'] = number_format($assArr['buy_price'], '2', '.', '');//设备原值
        $row['expenditure'] = number_format($assArr['buy_price'] - $row['average_profit'], '2', '.', '');//原值-平均利润(半圆灰色部分)
        if ($row['buy_price'] > 0) {
            $average_profit = $row['average_profit'] / $row['buy_price'];
            if (strpos($average_profit, 'e') or strpos($average_profit, 'E')) {
                $row['average_profit_title'] = '回报率约0%';
            } else {
                $row['average_profit_title'] = '回报率' . (number_format($average_profit, 4, '.', '') * 100) . '%';
            }
        } else {
            $row['average_profit_title'] = '回报率0%';
        }
        if ($row['average_profit'] < 0) {
            $row['average_profit_color'] = '#FF3030';//亏损红色
        }
        //阳性率
        $row['not_positive_num'] = $row['work_num'] - $row['positive_num'];//非阳性次数
        $row['positive_num_title'] = $row['work_num'] > 0 ? '阳性率' . (number_format($row['positive_num'] / $row['work_num'], 4, '.', '') * 100) . '%' : '阳性率0%';
        //避免饼图出现阳性0次也显示: 阳性0(50%) 非阳性0(50%)
        if ($row['positive_num'] == 0 && $row['work_num'] == 0) {
            $row['not_positive_num_show'] = 1;
        } else {
            $row['not_positive_num_show'] = $row['not_positive_num'];
        }

        //饼图公用样式
        $row['label']['normal']['show'] = true;
        $row['label']['normal']['position'] = 'center';
        $row['label']['normal']['textStyle']['fontSize'] = 17;
        $row['label']['normal']['textStyle']['fontWeight'] = 'bold';
        $row['label']['emphasis']['show'] = true;
        $row['label']['emphasis']['textStyle']['fontSize'] = 17;
        $row['label']['emphasis']['textStyle']['fontWeight'] = 'bold';
        $result['msg'] = '获取成功';
        $result['status'] = 1;
        $result['row'] = $row;
        return $result;
    }

    //单机效益分析 混合
    public function assetsBenefitDataGetFix()
    {
        $year = I('POST.year');
        $assnum = I('POST.assnum');
        $where['assnum'] = array('EQ', $assnum);
        if ($year) {
            $where['entryDate'][] = array('EGT', $year . '-01');
            $where['entryDate'][] = array('ELT', $year . '-12');
        }
        //设备收入明细
        $fields = "assnum,entryDate,income,depreciation_cost,material_cost,maintenance_cost,management_cost,comprehensive_cost,interest_cost,work_number,work_day";
        $data = $this->DB_get_all('assets_benefit', $fields, $where, '', 'entryDate ASC');
        if (!$data) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        if ($year) {
            $result = $this->format_year_fixdata($data, $year);
        } else {
            $result = $this->format_all_fixdata($data);
        }
        return $result;
    }

    public function format_year_fixdata($data, $year)
    {
        //总收益情况
        $income_xAxis = [];
        foreach ($data as $k => $v) {
            $entryDate = $v['entryDate'];
            if (!in_array($entryDate, $income_xAxis)) {
                $income_xAxis[] = $entryDate;
                $income[$entryDate]['income'] = 0;
                $income[$entryDate]['work_number'] = 0;
                $income[$entryDate]['all_cost'] = 0;
                $income[$entryDate]['work_days'] = 0;
                $income[$entryDate]['income'] += $v['income'];
                $income[$entryDate]['work_number'] += $v['work_number'];
                $income[$entryDate]['work_days'] += $v['work_day'];
                $income[$entryDate]['all_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                $income[$entryDate]['profit'] = $income[$entryDate]['income'] - $income[$entryDate]['all_cost'];

            } else {
                $income[$entryDate]['income'] += $v['income'];
                $income[$entryDate]['work_number'] += $v['work_number'];
                $income[$entryDate]['work_days'] += $v['work_day'];
                $income[$entryDate]['all_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                $income[$entryDate]['profit'] = $income[$entryDate]['income'] - $income[$entryDate]['all_cost'];
            }
        }
        $income_series_data = [];
        $total_income = $total_cost = 0;
        foreach ($income as $v) {
            $total_income += $v['income'];
            $total_cost += $v['all_cost'];
            $income_series_data['income'][] = round($v['income'] / 10000, 2);
            $income_series_data['work_number'][] = $v['work_number'];
            $income_series_data['work_days'][] = $v['work_days'];
            $income_series_data['all_cost'][] = round($v['all_cost'] / 10000, 2);
            $income_series_data['profit'][] = round($v['profit'] / 10000, 2);
        }
        $income_series_data['total_days'] = cal_days_in_year($year);
        $income_data['series_data'] = $income_series_data;
        $income_data['xAxis_data'] = $income_xAxis;
        $income_data['total_income'] = round($total_income / 10000, 2);
        $income_data['total_cost'] = round($total_cost / 10000, 2);
        $income_data['total_profit'] = round(($total_income - $total_cost) / 10000, 2);
        $income_data['total_rate_profit'] = round(($total_income - $total_cost) / $total_income * 100, 2);

        //支出分布
        $depreciation_cost = $material_cost = $maintenance_cost = $management_cost = $comprehensive_cost = $interest_cost = 0;
        foreach ($data as $k => $v) {
            $depreciation_cost += $v['depreciation_cost'];
            $material_cost += $v['material_cost'];
            $maintenance_cost += $v['maintenance_cost'];
            $management_cost += $v['management_cost'];
            $comprehensive_cost += $v['comprehensive_cost'];
            $interest_cost += $v['interest_cost'];
        }
        $cost_data = [];
        $cost_data['legend'] = ['折旧费用', '材料费用', '维保费用', '管理费用', '综合费用', '利息支出'];
        $cost_data['series_data'] = [
            ['name' => '折旧费用', 'value' => round($depreciation_cost / 10000, 2)],
            ['name' => '材料费用', 'value' => round($material_cost / 10000, 2)],
            ['name' => '维保费用', 'value' => round($maintenance_cost / 10000, 2)],
            ['name' => '管理费用', 'value' => round($management_cost / 10000, 2)],
            ['name' => '综合费用', 'value' => round($comprehensive_cost / 10000, 2)],
            ['name' => '利息支出', 'value' => round($interest_cost / 10000, 2)],
        ];

        //收支结余
        $jieyu_cost = $jieyu_profit = [];
        foreach ($income_data['series_data']['income'] as $k => $v) {
            $jieyu_cost[] = $income_data['series_data']['all_cost'][$k] > 0 ? -$income_data['series_data']['all_cost'][$k] : 0;
            $jieyu_profit[] = $v - $income_data['series_data']['all_cost'][$k];
        }
        $jieyu_data['jieyu_income'] = $income_data['series_data']['income'];
        $jieyu_data['jieyu_cost'] = $jieyu_cost;
        $jieyu_data['jieyu_profit'] = $jieyu_profit;

        $result['msg'] = '获取成功';
        $result['status'] = 1;
        $result['row']['income_data'] = $income_data;
        $result['row']['cost_data'] = $cost_data;
        $result['row']['jieyu_data'] = $jieyu_data;
        return $result;
    }

    public function format_all_fixdata($data)
    {
        //总收益情况
        $income_xAxis = [];
        foreach ($data as $k => $v) {
            $tj_year = explode('-', $v['entryDate']);
            if (!in_array($tj_year[0], $income_xAxis)) {
                $income_xAxis[] = $tj_year[0];
                $income[$tj_year[0]]['income'] = 0;
                $income[$tj_year[0]]['work_number'] = 0;
                $income[$tj_year[0]]['all_cost'] = 0;
                $income[$tj_year[0]]['work_days'] = 0;
                $income[$tj_year[0]]['income'] += $v['income'];
                $income[$tj_year[0]]['work_number'] += $v['work_number'];
                $income[$tj_year[0]]['work_days'] += $v['work_day'];
                $income[$tj_year[0]]['all_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                $income[$tj_year[0]]['profit'] = $income[$tj_year[0]]['income'] - $income[$tj_year[0]]['all_cost'];
            } else {
                $income[$tj_year[0]]['income'] += $v['income'];
                $income[$tj_year[0]]['work_number'] += $v['work_number'];
                $income[$tj_year[0]]['work_days'] += $v['work_day'];
                $income[$tj_year[0]]['all_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                $income[$tj_year[0]]['profit'] = $income[$tj_year[0]]['income'] - $income[$tj_year[0]]['all_cost'];
            }
        }
        $income_series_data = [];
        $total_income = $total_cost = 0;
        foreach ($income as $v) {
            $total_income += $v['income'];
            $total_cost += $v['all_cost'];
            $income_series_data['income'][] = round($v['income'] / 10000, 2);
            $income_series_data['work_number'][] = $v['work_number'];
            $income_series_data['work_days'][] = $v['work_days'];
            $income_series_data['all_cost'][] = round($v['all_cost'] / 10000, 2);
            $income_series_data['profit'][] = round($v['profit'] / 10000, 2);
        }
        $income_series_data['total_days'] = 0;
        foreach ($income_xAxis as $v) {
            $income_series_data['total_days'] += cal_days_in_year($v);
        }
        $income_data['series_data'] = $income_series_data;
        $income_data['xAxis_data'] = $income_xAxis;
        $income_data['total_income'] = round($total_income / 10000, 2);
        $income_data['total_cost'] = round($total_cost / 10000, 2);
        $income_data['total_profit'] = round(($total_income - $total_cost) / 10000, 2);
        $income_data['total_rate_profit'] = round(($total_income - $total_cost) / $total_income * 100, 2);

        //支出分布
        $depreciation_cost = $material_cost = $maintenance_cost = $management_cost = $comprehensive_cost = $interest_cost = 0;
        foreach ($data as $k => $v) {
            $depreciation_cost += $v['depreciation_cost'];
            $material_cost += $v['material_cost'];
            $maintenance_cost += $v['maintenance_cost'];
            $management_cost += $v['management_cost'];
            $comprehensive_cost += $v['comprehensive_cost'];
            $interest_cost += $v['interest_cost'];
        }
        $cost_data = [];
        $cost_data['legend'] = ['折旧费用', '材料费用', '维保费用', '管理费用', '综合费用', '利息支出'];
        $cost_data['series_data'] = [
            ['name' => '折旧费用', 'value' => round($depreciation_cost / 10000, 2)],
            ['name' => '材料费用', 'value' => round($material_cost / 10000, 2)],
            ['name' => '维保费用', 'value' => round($maintenance_cost / 10000, 2)],
            ['name' => '管理费用', 'value' => round($management_cost / 10000, 2)],
            ['name' => '综合费用', 'value' => round($comprehensive_cost / 10000, 2)],
            ['name' => '利息支出', 'value' => round($interest_cost / 10000, 2)],
        ];

        //收支结余
        $jieyu_cost = $jieyu_profit = [];
        foreach ($income_data['series_data']['income'] as $k => $v) {
            $jieyu_cost[] = $income_data['series_data']['all_cost'][$k] > 0 ? -$income_data['series_data']['all_cost'][$k] : 0;
            $jieyu_profit[] = $v - $income_data['series_data']['all_cost'][$k];
        }
        $jieyu_data['jieyu_income'] = $income_data['series_data']['income'];
        $jieyu_data['jieyu_cost'] = $jieyu_cost;
        $jieyu_data['jieyu_profit'] = $jieyu_profit;

        $result['msg'] = '获取成功';
        $result['status'] = 1;
        $result['row']['income_data'] = $income_data;
        $result['row']['cost_data'] = $cost_data;
        $result['row']['jieyu_data'] = $jieyu_data;
        return $result;
    }

    /**
     * 获取效益明细记录
     * @return mixed
     */
    public function getBenfitDetail()
    {
        $limit = I('post.limit') ? I('post.limit') : C('DEFAULT_LIMIT');
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $year = I('POST.year');
        $assnum = I('POST.assnum');
        $where['assnum'] = array('EQ', $assnum);
        if ($year) {
            $where['entryDate'][] = array('EGT', $year . '-01');
            $where['entryDate'][] = array('ELT', $year . '-12');
        }
        //设备收入明细
        $fields = "entryDate,income,depreciation_cost,material_cost,maintenance_cost,management_cost,comprehensive_cost,interest_cost,work_number,work_day,operator";
        $total = $this->DB_get_count('assets_benefit', $where);
        $data = $this->DB_get_all('assets_benefit', $fields, $where, '', 'entryDate ASC', $offset . "," . $limit);
        if (!$data) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        foreach ($data as &$v) {
            $all_cost = $v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost'];
            $v['all_cost'] = $all_cost;
            $v['profit'] = $v['income'] - $all_cost;
            $v['profit_rate'] = round(($v['income'] - $all_cost) / $v['income'] * 100, 2) . '%';
            $tj_year = explode('-', $v['entryDate']);
            $days = cal_days_in_month(CAL_GREGORIAN, (int)$tj_year[1], $tj_year[0]);
            $v['use_rate'] = round($v['work_day'] / $days * 100, 2) . '%';
        }
        $result["code"] = 200;
        $result['total'] = $total;
        $result["offset"] = $offset;
        $result["limit"] = $limit;
        $result["rows"] = $data;
        return $result;
    }

    //单机效益分析 线图
    public function assetsBenefitDataGetLine()
    {
        $assnum = I('POST.assnum');
        $yearDate = I('POST.yearDate');

        $startDate = $yearDate . '-01';
        $endDate = $yearDate . '-12';

        $d1 = strtotime($startDate);
        $d2 = strtotime(date("Y-m", strtotime($endDate . "+1 month")));
        //计算总共有多少天
        $Days = round(($d2 - $d1) / 3600 / 24);

        $where['assnum'] = array('EQ', $assnum);
        $where['entryDate'][] = array('EGT', $startDate);
        $where['entryDate'][] = array('ELT', $endDate);
        //设备收入明细
        $fields = 'entryDate,income,work_number,positive_rate as positive_rate_num,depreciation_cost,material_cost,maintenance_cost,
        management_cost,interest_cost,comprehensive_cost,operator,work_day';
        $benefit = $this->DB_get_all('assets_benefit', $fields, $where, '', 'entryDate');

        //设备明细
        $data = [];
        foreach ($benefit as &$one) {
            $data[$one['entryDate']]['income'] = $one['income'];
            $data[$one['entryDate']]['cost'] += $one['depreciation_cost'] + $one['material_cost'] + $one['maintenance_cost'] + $one['management_cost'] + $one['comprehensive_cost'] + $one['interest_cost'];
            $data[$one['entryDate']]['profit'] = $data[$one['entryDate']]['income'] - $data[$one['entryDate']]['cost'];
        }

        $row = [];
        for ($i = 1; $i <= 12; $i++) {
            $income = $data[$startDate]['income'] ?: 0;
            $cost = $data[$startDate]['cost'] ?: 0;
            $profit = $data[$startDate]['profit'] ?: 0;
            $row['income'][] = number_format($income, '2', '.', '');
            $row['cost'][] = number_format($cost, '2', '.', '');
            $row['profit'][] = number_format($profit, '2', '.', '');
            $startDate = date("Y-m", strtotime($startDate . "+1 month"));
        }
        if ($benefit) {
            $tabData = [];
            $tabData['entryDate'] = '<span class="rquireCoin">合计</span>';
            foreach ($benefit as &$one) {
                $tabData['income'] = number_format($tabData['income'] += $one['income'], '2', '.', '');
                $tabData['depreciation_cost'] = number_format($tabData['depreciation_cost'] += $one['depreciation_cost'], '2', '.', '');
                $tabData['material_cost'] = number_format($tabData['material_cost'] += $one['material_cost'], '2', '.', '');
                $tabData['maintenance_cost'] = number_format($tabData['maintenance_cost'] += $one['maintenance_cost'], '2', '.', '');
                $tabData['management_cost'] = number_format($tabData['management_cost'] += $one['management_cost'], '2', '.', '');
                $tabData['operator'] += $one['operator'];
                $tabData['comprehensive_cost'] = number_format($tabData['comprehensive_cost'] += $one['comprehensive_cost'], '2', '.', '');
                $tabData['interest_cost'] = number_format($tabData['interest_cost'] += $one['interest_cost'], '2', '.', '');


                //当月总费用
                $one['all_cost'] += $one['depreciation_cost'] + $one['material_cost'] + $one['maintenance_cost'] + $one['management_cost'] + $one['comprehensive_cost'] + $one['interest_cost'];
                $one['all_cost'] = number_format($one['all_cost'], '2', '.', '');
                //总费用
                $tabData['all_cost'] = number_format($tabData['all_cost'] += $one['all_cost'], '2', '.', '');


                $one['repair_time'] = 0;//todo 维修时常

                //总维修时常
                $tabData['repair_time'] += $one['repair_time'];

                //拆分月份和年份 用于计算当月天数
                $toDate = explode('-', $one['entryDate']);

                //当月使用率
                $one['workDay_rate'] = (number_format($one['work_day'] / cal_days_in_month(CAL_GREGORIAN, $toDate[1], $toDate[0]), 4, '.', '') * 100) . '%';

                //总工作天数
                $tabData['work_day'] += $one['work_day'];

                //当月阳性率
                $one['positive_rate'] = $one['work_number'] > 0 ? (number_format($one['positive_rate_num'] / $one['work_number'], 4, '.', '') * 100) . '%' : '0%';

                //总诊疗次数
                $tabData['work_number'] += $one['work_number'];

                //总阳性次数
                $tabData['positive_rate_num'] += $one['positive_rate_num'];

                //当月结余
                $one['profit'] = number_format($one['income'] - $one['all_cost'], '2', '.', '');


                //当月结余率
                if ($one['income'] > 0) {
                    $one['profitr_rate'] = (number_format($one['profit'] / $one['income'], 4, '.', '') * 100) . '%';
                } else {
                    $one['profitr_rate'] = '0%';
                }


                $tabData['profit'] = number_format($tabData['profit'] += $one['profit'], '2', '.', '');

            }

            //总使用率
            $tabData['workDay_rate'] = (number_format($tabData['work_day'] / $Days, 4, '.', '') * 100) . '%';

            //总阳性率
            $tabData['positive_rate'] = $tabData['work_number'] > 0 ? (number_format($tabData['positive_rate_num'] / $tabData['work_number'], 4, '.', '') * 100) . '%' : '0%';

            //总结余率
            if ($tabData['income'] > 0) {
                $tabData['profitr_rate'] = (number_format($tabData['profit'] / $tabData['income'], 4, '.', '') * 100) . '%';
            } else {
                $tabData['profitr_rate'] = '0%';
            }

            //追加进入原数组
            array_push($benefit, $tabData);
        }

        $result['msg'] = '获取成功';
        $result['status'] = 1;
        $result['row']['data'] = $benefit;
        $result['row']['line'] = $row;
        return $result;
    }

    //科室效益分析 线图
    public function departmentBenefitDataGetLine()
    {

        $departid = I('POST.departid');
        $yearDate = I('POST.yearDate');
        $startDate = $yearDate . '-01';
        $endDate = $yearDate . '-12';

        $d1 = strtotime($startDate);
        $d2 = strtotime(date("Y-m", strtotime($endDate . "+1 month")));
        //计算总共有多少天
        $Days = round(($d2 - $d1) / 3600 / 24);
        $where['departid'] = array('EQ', $departid);
        $where['entryDate'][] = array('EGT', $startDate);
        $where['entryDate'][] = array('ELT', $endDate);
        //设备收入明细

        $fields = 'entryDate,income,work_number,positive_rate as positive_rate_num,depreciation_cost,material_cost,maintenance_cost,
        management_cost,interest_cost,comprehensive_cost,operator,work_day';
        //科室明细
        $benefit = $this->DB_get_all('assets_benefit', $fields, $where, '', 'entryDate');
        //===================================================================================START
        //将同月份的归类 并且按照月份排序重置$benefit
        $newBenefit = [];

        foreach ($benefit as &$one) {
            if ($newBenefit[$one['entryDate']]['entryDate']) {
                $newBenefit[$one['entryDate']]['income'] += $one['income'];
                $newBenefit[$one['entryDate']]['work_number'] += $one['work_number'];
                $newBenefit[$one['entryDate']]['positive_rate_num'] += $one['positive_rate_num'];
                $newBenefit[$one['entryDate']]['depreciation_cost'] += $one['depreciation_cost'];
                $newBenefit[$one['entryDate']]['material_cost'] += $one['material_cost'];
                $newBenefit[$one['entryDate']]['maintenance_cost'] += $one['maintenance_cost'];
                $newBenefit[$one['entryDate']]['management_cost'] += $one['management_cost'];
                $newBenefit[$one['entryDate']]['interest_cost'] += $one['interest_cost'];
                $newBenefit[$one['entryDate']]['comprehensive_cost'] += $one['comprehensive_cost'];
                $newBenefit[$one['entryDate']]['operator'] += $one['operator'];
                $newBenefit[$one['entryDate']]['work_day'] += $one['work_day'];
                $newBenefit[$one['entryDate']]['departNum']++;
            } else {
                $newBenefit[$one['entryDate']]['entryDate'] = $one['entryDate'];
                $newBenefit[$one['entryDate']]['income'] = $one['income'];
                $newBenefit[$one['entryDate']]['work_number'] = $one['work_number'];
                $newBenefit[$one['entryDate']]['positive_rate_num'] = $one['positive_rate_num'];
                $newBenefit[$one['entryDate']]['depreciation_cost'] = $one['depreciation_cost'];
                $newBenefit[$one['entryDate']]['material_cost'] = $one['material_cost'];
                $newBenefit[$one['entryDate']]['maintenance_cost'] = $one['maintenance_cost'];
                $newBenefit[$one['entryDate']]['management_cost'] = $one['management_cost'];
                $newBenefit[$one['entryDate']]['interest_cost'] = $one['interest_cost'];
                $newBenefit[$one['entryDate']]['comprehensive_cost'] = $one['comprehensive_cost'];
                $newBenefit[$one['entryDate']]['operator'] = $one['operator'];
                $newBenefit[$one['entryDate']]['work_day'] = $one['work_day'];
                $newBenefit[$one['entryDate']]['departNum'] = 1;
            }
        }

        array_multisort(array_column($newBenefit, 'entryDate'), SORT_ASC, $newBenefit);
        $benefit = array_values($newBenefit);
        //===================================================================================END
        $data = [];
        foreach ($benefit as &$one) {
            $data[$one['entryDate']]['income'] = $one['income'];
            $data[$one['entryDate']]['cost'] += $one['depreciation_cost'] + $one['material_cost'] + $one['maintenance_cost'] + $one['management_cost'] + $one['comprehensive_cost'] + $one['interest_cost'];
            $data[$one['entryDate']]['profit'] = $data[$one['entryDate']]['income'] - $data[$one['entryDate']]['cost'];
        }

        $row = [];
        for ($i = 1; $i <= 12; $i++) {
            $income = $data[$startDate]['income'] ?: 0;
            $cost = $data[$startDate]['cost'] ?: 0;
            $profit = $data[$startDate]['profit'] ?: 0;
            $row['income'][] = number_format($income, '2', '.', '');
            $row['cost'][] = number_format($cost, '2', '.', '');
            $row['profit'][] = number_format($profit, '2', '.', '');
            $startDate = date("Y-m", strtotime($startDate . "+1 month"));
        }
        if ($benefit) {
            $tabData = [];
            $tabData['entryDate'] = '<span class="rquireCoin">合计</span>';
            foreach ($benefit as &$one) {

                $tabData['income'] = number_format($tabData['income'] += $one['income'], '2', '.', '');
                $tabData['depreciation_cost'] = number_format($tabData['depreciation_cost'] += $one['depreciation_cost'], '2', '.', '');
                $tabData['material_cost'] = number_format($tabData['material_cost'] += $one['material_cost'], '2', '.', '');
                $tabData['maintenance_cost'] = number_format($tabData['maintenance_cost'] += $one['maintenance_cost'], '2', '.', '');
                $tabData['management_cost'] = number_format($tabData['management_cost'] += $one['management_cost'], '2', '.', '');
                $tabData['operator'] += $one['operator'];
                $tabData['comprehensive_cost'] = number_format($tabData['comprehensive_cost'] += $one['comprehensive_cost'], '2', '.', '');
                $tabData['interest_cost'] = number_format($tabData['interest_cost'] += $one['interest_cost'], '2', '.', '');

                //当月总费用
                $one['all_cost'] += $one['depreciation_cost'] + $one['material_cost'] + $one['maintenance_cost'] + $one['management_cost'] + $one['comprehensive_cost'] + $one['interest_cost'];
                $one['all_cost'] = number_format($one['all_cost'], '2', '.', '');

                //总费用
                $tabData['all_cost'] = number_format($tabData['all_cost'] += $one['all_cost'], '2', '.', '');


                $one['repair_time'] = 0;//todo 维修时常

                //总维修时常
                $tabData['repair_time'] += $one['repair_time'];

                //拆分月份和年份 用于计算当月天数
                $toDate = explode('-', $one['entryDate']);

                $tabData['all_day'] += ($one['departNum'] * cal_days_in_month(CAL_GREGORIAN, $toDate[1], $toDate[0]));

                //当月使用率
                $one['workDay_rate'] = (number_format($one['work_day'] / ($one['departNum'] * cal_days_in_month(CAL_GREGORIAN, $toDate[1], $toDate[0])), 4, '.', '') * 100) . '%';

                //总工作天数
                $tabData['work_day'] += $one['work_day'];

                //当月阳性率
                $one['positive_rate'] = $one['work_number'] > 0 ? (number_format($one['positive_rate_num'] / $one['work_number'], 4, '.', '') * 100) . '%' : '0%';

                //总诊疗次数
                $tabData['work_number'] += $one['work_number'];

                //总阳性次数
                $tabData['positive_rate_num'] += $one['positive_rate_num'];

                //当月结余
                $one['profit'] = number_format($one['income'] - $one['all_cost'], '2', '.', '');

                //当月结余率
                if ($one['income'] > 0) {
                    $one['profitr_rate'] = (number_format($one['profit'] / $one['income'], 4, '.', '') * 100) . '%';
                } else {
                    $one['profitr_rate'] = '0%';
                }

                //总结余
                $tabData['profit'] = number_format($tabData['profit'] += $one['profit'], '2', '.', '');

            }
            //总使用率
            $tabData['workDay_rate'] = (number_format($tabData['work_day'] / $tabData['all_day'], 4, '.', '') * 100) . '%';

            //总阳性率
            $tabData['positive_rate'] = $tabData['work_number'] > 0 ? (number_format($tabData['positive_rate_num'] / $tabData['work_number'], 4, '.', '') * 100) . '%' : '0%';

            //总结余率
            if ($tabData['income'] > 0) {
                $tabData['profitr_rate'] = (number_format($tabData['profit'] / $tabData['income'], 4, '.', '') * 100) . '%';
            } else {
                $tabData['profitr_rate'] = '0%';
            }

            //追加进入原数组
            array_push($benefit, $tabData);
        }

        $result['msg'] = '获取成功';
        $result['status'] = 1;
        $result['row']['data'] = $benefit;
        $result['row']['line'] = $row;
        return $result;
    }

    /**
     * 科室效益设备分析
     */
    public function departmentBenefitData()
    {
        $year = I('POST.year');
        $departid = I('POST.departid');
        $where['departid'] = array('EQ', $departid);
        if ($year) {
            $where['entryDate'][] = array('EGT', $year . '-01');
            $where['entryDate'][] = array('ELT', $year . '-12');
        }
        //设备收入明细
        $fields = "assnum,departid,entryDate,income,depreciation_cost,material_cost,maintenance_cost,management_cost,comprehensive_cost,interest_cost,work_number,work_day";
        $data = $this->DB_get_all('assets_benefit', $fields, $where, '', 'entryDate ASC');
        if (!$data) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }

        //总收益情况
        $income_xAxis = [];
        foreach ($data as $k => $v) {
            if($year){
                $xAxis = $v['entryDate'];
            }else{
                $tj_year = explode('-', $v['entryDate']);
                $xAxis = $tj_year[0];
            }

            if (!in_array($xAxis, $income_xAxis)) {
                $income_xAxis[] = $xAxis;
                $income[$xAxis]['income'] = 0;
                $income[$xAxis]['maintenance_cost'] = 0;
                $income[$xAxis]['work_number'] = 0;
                $income[$xAxis]['all_cost'] = 0;
                $income[$xAxis]['work_days'] = 0;
                $income[$xAxis]['income'] += $v['income'];
                $income[$xAxis]['work_number'] += $v['work_number'];
                $income[$xAxis]['work_days'] += $v['work_day'];
                $income[$xAxis]['all_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                $income[$xAxis]['profit'] = $income[$xAxis]['income'] - $income[$xAxis]['all_cost'];
            } else {
                $income[$xAxis]['income'] += $v['income'];
                $income[$xAxis]['maintenance_cost'] += $v['maintenance_cost'];
                $income[$xAxis]['work_number'] += $v['work_number'];
                $income[$xAxis]['work_days'] += $v['work_day'];
                $income[$xAxis]['all_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
                $income[$xAxis]['profit'] = $income[$xAxis]['income'] - $income[$xAxis]['all_cost'];
            }
        }
        $benefit_1_data = [];
        $benefit_2_data = [];
        $benefit_1_data['xAxis_data'] = $income_xAxis;
        $benefit_2_data['xAxis_data'] = $income_xAxis;
        foreach ($income as $v) {
            $benefit_1_data['income'][] = round($v['income'] / 10000, 2);
            $benefit_2_data['income'][] = round($v['income'] / 10000, 2);
            $benefit_2_data['all_cost'][] = -round($v['all_cost'] / 10000, 2);
            $benefit_2_data['profit'][] = round($v['profit'] / 10000, 2);
        }

        $result['benefit_1_data'] = $benefit_1_data;
        $result['benefit_2_data'] = $benefit_2_data;

        //效益设备收益
        $result['benefit_3_data'] = $this->getBenefitAssetsIncome();

        //支出TOP5设备
        $result['benefit_4_data'] = $this->getTopFiveCostAssets($data);

        //维保费用支出
        $benefit_6_data['xAxis_data'] = $income_xAxis;
        foreach ($income as $v){
            $benefit_6_data['series_data'][] = round($v['maintenance_cost'] / 10000, 2);
        }
        $result['benefit_6_data'] = $benefit_6_data;

        return $result;
    }

    //获取科室所有效益设备的收入
    public function getBenefitAssetsIncome()
    {
        $year = I('POST.year');
        $departid = I('POST.departid');
        $as_where['hospital_id'] = session('current_hospitalid');
        $as_where['status'][0] = 'NOT IN';
        $as_where['status'][1][] = C('ASSETS_STATUS_SCRAP');
        $as_where['status'][1][] = C('ASSETS_STATUS_OUTSIDE');
        $as_where['status'][1][] = C('ASSETS_STATUS_OUTSIDE_ON');
        $as_where['is_delete'] = 0;
        $as_where['is_benefit'] = 1;
        $as_where['main_assid'] = 0;//主设备
        $as_where['departid'] = array('EQ', $departid);
        $assModel = new AssetsInfoModel();
        $benefitData = $assModel->DB_get_all('assets_info', 'assets,assnum', $as_where);
        if (!$benefitData) {
            return [];
        } else {
            $where['A.departid'] = array('EQ', $departid);
            if ($year) {
                $where['entryDate'][] = array('EGT', $year . '-01');
                $where['entryDate'][] = array('ELT', $year . '-12');
            }
            $join = " LEFT JOIN sb_assets_info AS B ON A.assnum = B.assnum";
            $fields = "B.assets,A.assnum,SUM(A.income) AS income";
            $data_3 = $this->DB_get_all_join('assets_benefit', 'A', $fields, $join, $where, 'A.assnum');

            foreach ($benefitData as $k => $v) {
                $benefitData[$k]['income'] = 0;
                foreach ($data_3 as $dv) {
                    if ($v['assnum'] == $dv['assnum']) {
                        $benefitData[$k]['income'] = $dv['income'];
                    }
                }
            }
            $d3 = array_column($benefitData, 'income');
            array_multisort($d3, SORT_DESC, $benefitData);
            foreach ($benefitData as $k => $v) {
                $benefit_3_data['yAxis_data'][] = $v['assets'];
                $benefit_3_data['income'][] = ($v['income'] > 0) ? round($v['income'] / 10000, 2) : 0;
            }
            $benefit_3_data['minHeight'] = 40 * count($benefitData);
            return $benefit_3_data;
        }
    }

    //获取科室支出前五设备
    public function getTopFiveCostAssets($data)
    {
        $assnum_cost = $assnum = [];
        foreach ($data as $k => $v) {
            if (!in_array($v['assnum'], $assnum)) {
                $assnum[] = $v['assnum'];
                $assnum_cost[$v['assnum']]['income'] = 0;
                $assnum_cost[$v['assnum']]['depreciation_cost'] = 0;
                $assnum_cost[$v['assnum']]['material_cost'] = 0;
                $assnum_cost[$v['assnum']]['maintenance_cost'] = 0;
                $assnum_cost[$v['assnum']]['management_cost'] = 0;
                $assnum_cost[$v['assnum']]['comprehensive_cost'] = 0;
                $assnum_cost[$v['assnum']]['interest_cost'] = 0;
                $assnum_cost[$v['assnum']]['total_cost'] = 0;

                $assnum_cost[$v['assnum']]['income'] += $v['income'];
                $assnum_cost[$v['assnum']]['depreciation_cost'] += $v['depreciation_cost'];
                $assnum_cost[$v['assnum']]['material_cost'] += $v['material_cost'];
                $assnum_cost[$v['assnum']]['maintenance_cost'] += $v['maintenance_cost'];
                $assnum_cost[$v['assnum']]['management_cost'] += $v['management_cost'];
                $assnum_cost[$v['assnum']]['comprehensive_cost'] += $v['comprehensive_cost'];
                $assnum_cost[$v['assnum']]['interest_cost'] += $v['interest_cost'];
                $assnum_cost[$v['assnum']]['total_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
            } else {
                $assnum_cost[$v['assnum']]['income'] += $v['income'];
                $assnum_cost[$v['assnum']]['income'] += $v['income'];
                $assnum_cost[$v['assnum']]['depreciation_cost'] += $v['depreciation_cost'];
                $assnum_cost[$v['assnum']]['material_cost'] += $v['material_cost'];
                $assnum_cost[$v['assnum']]['maintenance_cost'] += $v['maintenance_cost'];
                $assnum_cost[$v['assnum']]['management_cost'] += $v['management_cost'];
                $assnum_cost[$v['assnum']]['comprehensive_cost'] += $v['comprehensive_cost'];
                $assnum_cost[$v['assnum']]['interest_cost'] += $v['interest_cost'];
                $assnum_cost[$v['assnum']]['total_cost'] += ($v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['management_cost'] + $v['comprehensive_cost'] + $v['interest_cost']);
            }
        }
        $assnum_cost = arraySort($assnum_cost, 'total_cost', 'desc');
        $i = 0;
        $asModel = new AssetsInfoModel();
        foreach ($assnum_cost as $k => $v) {
            if ($i > 4) {
                unset($assnum_cost[$k]);
                break;
            }
            $i++;
            $as = $asModel->DB_get_one('assets_info', 'assets,assnum,model', ['assnum' => $k]);
            $assnum_cost[$k]['income'] = round($v['income'] / 10000, 2);
            $assnum_cost[$k]['total_cost'] = round($v['total_cost'] / 10000, 2);
            $assnum_cost[$k]['assets'] = $as['assets'];
            $assnum_cost[$k]['assnum'] = $as['assnum'];
            $assnum_cost[$k]['model'] = $as['model'];
        }
        $benefit_4_data = [];
        foreach ($assnum_cost as $v) {
            $benefit_4_data['yAxis_data'][] = $v['assets'];
            $benefit_4_data['total_cost'][] = $v['total_cost'];
        }
        $name = [
            'depreciation_cost' => '折旧费',
            'material_cost' => '材料费',
            'maintenance_cost' => '维保费',
            'management_cost' => '管理费',
            'comprehensive_cost' => '综合费',
            'interest_cost' => '利息'
        ];
        $j = 1;
        foreach ($assnum_cost as $k => $v) {
            $benefit_4_data['benefit_4_' . $j . '_data']['assets'] = $v['assets'];
            $benefit_4_data['benefit_4_' . $j . '_data']['legend'][] = $name['depreciation_cost'];
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][0]['value'] = round($v['depreciation_cost'] / 10000, 2);
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][0]['name'] = $name['depreciation_cost'];

            $benefit_4_data['benefit_4_' . $j . '_data']['legend'][] = $name['material_cost'];
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][1]['value'] = round($v['material_cost'] / 10000, 2);
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][1]['name'] = $name['material_cost'];

            $benefit_4_data['benefit_4_' . $j . '_data']['legend'][] = $name['maintenance_cost'];
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][2]['value'] = round($v['maintenance_cost'] / 10000, 2);
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][2]['name'] = $name['maintenance_cost'];

            $benefit_4_data['benefit_4_' . $j . '_data']['legend'][] = $name['management_cost'];
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][3]['value'] = round($v['management_cost'] / 10000, 2);
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][3]['name'] = $name['management_cost'];

            $benefit_4_data['benefit_4_' . $j . '_data']['legend'][] = $name['comprehensive_cost'];
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][4]['value'] = round($v['comprehensive_cost'] / 10000, 2);
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][4]['name'] = $name['comprehensive_cost'];

            $benefit_4_data['benefit_4_' . $j . '_data']['legend'][] = $name['interest_cost'];
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][5]['value'] = round($v['interest_cost'] / 10000, 2);
            $benefit_4_data['benefit_4_' . $j . '_data']['series'][5]['name'] = $name['interest_cost'];
            $j++;
        }
        return $benefit_4_data;
    }

    //获取回本设备
    public function getHuiBenAssets()
    {
        $departid = I('POST.departid');
        $where['hospital_id'] = session('current_hospitalid');
        $where['status'][0] = 'NOT IN';
        $where['status'][1][] = C('ASSETS_STATUS_SCRAP');
        $where['status'][1][] = C('ASSETS_STATUS_OUTSIDE');
        $where['status'][1][] = C('ASSETS_STATUS_OUTSIDE_ON');
        $where['is_delete'] = 0;
        $where['is_benefit'] = 1;
        $where['main_assid'] = 0;//主设备
        $where['departid'] = array('EQ', $departid);
        $assModel = new AssetsInfoModel();
        $assData = $assModel->DB_get_all('assets_info', 'assid,assnum,assets,model,expected_life,departid,buy_price,is_benefit', $where);
        $benefit_5_data = [];
        $i = 0;
        foreach ($assData as $k => $v) {
            if(!$v['buy_price']){
                //没设置设备价格
                continue;
            }
            $benefitData = $assModel->DB_get_all('assets_benefit', 'assnum,entryDate,income,depreciation_cost,material_cost,maintenance_cost,management_cost,comprehensive_cost,interest_cost', ['assnum' => $v['assnum']], '', 'entryDate asc');
            $income = 0;
            $total_cost = 0;
            $months = 0;
            foreach ($benefitData as $bv) {
                $months++;
                $income += $bv['income'];
                $total_cost += ($bv['depreciation_cost'] + $bv['material_cost'] + $bv['maintenance_cost'] + $bv['management_cost'] + $bv['comprehensive_cost'] + $bv['interest_cost']);
                if(($income - $total_cost) >= $v['buy_price']){
                    //净利润大于设备价格，已回本，记录设备信息
                    $benefit_5_data['xAxis_data'][] = $v['assets'];
                    $benefit_5_data['series']['buy_price'][] = round($v['buy_price']/10000,2);
                    $benefit_5_data['series']['huiben_years'][] = round($months/12,1);
                    //统计总利润
                    $profit = $benefitData = $assModel->DB_get_one('assets_benefit', 'sum(income) as income,sum(depreciation_cost+material_cost+maintenance_cost+management_cost+comprehensive_cost+interest_cost) as total_cost', ['assnum' => $v['assnum']]);
                    $benefit_5_data['series']['profit'][] = round(($profit['income'] - $profit['total_cost'])/10000,2);
                    //返回table数据
                    $benefit_5_data['table_data'][$i]['assets'] = $v['assets'];
                    $benefit_5_data['table_data'][$i]['assnum'] = $v['assnum'];
                    $benefit_5_data['table_data'][$i]['model'] = $v['model'];
                    $benefit_5_data['table_data'][$i]['expected_life'] = $v['expected_life'];
                    $benefit_5_data['table_data'][$i]['buy_price'] = round($v['buy_price']/10000,2);
                    $benefit_5_data['table_data'][$i]['income'] = round($profit['income']/10000,2);
                    $benefit_5_data['table_data'][$i]['total_cost'] = round($profit['total_cost']/10000,2);
                    $benefit_5_data['table_data'][$i]['profit'] = round(($profit['income'] - $profit['total_cost'])/10000,2);
                    $benefit_5_data['table_data'][$i]['huiben_years'] = round($months/12,1);
                    $i++;
                    break;
                }
            }
        }
        return $benefit_5_data;
    }

    /**
     * 获取科室效益设备
     */
    public function getBenfitAssets()
    {
        $limit = I('post.limit') ? I('post.limit') : 10;
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $order = I('post.order');
        $sort = I('post.sort');
        $departid = I('POST.departid');
        $year = I('POST.year');
        $where['hospital_id'] = session('current_hospitalid');
        $where['status'][0] = 'NOT IN';
        $where['status'][1][] = C('ASSETS_STATUS_SCRAP');
        $where['status'][1][] = C('ASSETS_STATUS_OUTSIDE');
        $where['status'][1][] = C('ASSETS_STATUS_OUTSIDE_ON');
        $where['is_delete'] = 0;
        $where['is_benefit'] = 1;
        $where['main_assid'] = 0;//主设备
        $where['departid'] = array('EQ', $departid);
        $assModel = new AssetsInfoModel();
        $total = $assModel->DB_get_count('assets_info', $where);
        $assData = $assModel->DB_get_all('assets_info', 'assid,assets,assnum,model,serialnum,opendate,expected_life,departid,buy_price,is_benefit', $where, '', $sort . ' ' . $order, $offset . "," . $limit);
        if (!$assData) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        $field = "sum(income) as income,sum(depreciation_cost+material_cost+maintenance_cost+management_cost+comprehensive_cost+interest_cost) as total_cost";
        foreach ($assData as &$v) {
            $b_where['assnum'] = $v['assnum'];
            if ($year) {
                $b_where['entryDate'][] = array('EGT', $year . '-01');
                $b_where['entryDate'][] = array('ELT', $year . '-12');
            }
            $benefitData = $assModel->DB_get_one('assets_benefit', $field,$b_where);
            $v['income'] = round($benefitData['income'] / 10000, 2);
            $v['all_cost'] = round($benefitData['total_cost'] / 10000, 2);
        }
        $result['total'] = (int)$total;
        $result["offset"] = $offset;
        $result["limit"] = (int)$limit;
        $result["code"] = 200;
        $result['rows'] = $assData;
        return $result;
    }

    //获取待入库设备明细信息
    public function getWatingUploadBenefit()
    {
        $limit = I('post.limit') ? I('post.limit') : 10;
        $page = I('post.page') ? I('post.page') : 1;
        $offset = ($page - 1) * $limit;
        $where['adduser'] = session('username');
        $where['is_save'] = C('NO_STATUS');//获取未上传的数据
        $asModel = new AssetsInfoModel();
        $total = $asModel->DB_get_count('assets_benefit_upload_temp', $where);
        //查询上次未完成保存的数据
        $assets = $asModel->DB_get_all('assets_benefit_upload_temp', '*', $where, '', 'entryDate asc,adddate asc', $offset . ',' . $limit);
        if (!$assets) {
            $result['msg'] = '暂无相关数据';
            $result['code'] = 400;
            return $result;
        }
        //判断待上传数据是否合法
        foreach ($assets as $k => $v) {
            if ($v['income'] < 0 && !checkPrice($v['income'])) {
                //月收入不合法
                $assets[$k]['income'] = '<span class="rquireCoin">' . $v['income'] . '</span>';
            }
            if (!judgeNum($v['work_number'])) {
                //工作天数
                $assets[$k]['work_number'] = '<span class="rquireCoin">' . $v['work_number'] . '</span>';
            }
            if ($v['depreciation_cost'] < 0 && !checkPrice($v['depreciation_cost'])) {
                //折旧费用不合法
                $assets[$k]['depreciation_cost'] = '<span class="rquireCoin">' . $v['depreciation_cost'] . '</span>';
            }
            if ($v['material_cost'] < 0 && !checkPrice($v['material_cost'])) {
                //材料费用不合法
                $assets[$k]['material_cost'] = '<span class="rquireCoin">' . $v['material_cost'] . '</span>';
            }
            if ($v['maintenance_cost'] < 0 && !checkPrice($v['maintenance_cost'])) {
                //维保费用不合法
                $assets[$k]['maintenance_cost'] = '<span class="rquireCoin">' . $v['maintenance_cost'] . '</span>';
            }
            if ($v['management_cost'] < 0 && !checkPrice($v['management_cost'])) {
                //管理费用不合法
                $assets[$k]['management_cost'] = '<span class="rquireCoin">' . $v['management_cost'] . '</span>';
            }
            if ($v['comprehensive_cost'] < 0 && !checkPrice($v['comprehensive_cost'])) {
                //综合费用不合法
                $assets[$k]['comprehensive_cost'] = '<span class="rquireCoin">' . $v['comprehensive_cost'] . '</span>';
            }
            if ($v['interest_cost'] < 0 && !checkPrice($v['interest_cost'])) {
                //利息支出不合法
                $assets[$k]['interest_cost'] = '<span class="rquireCoin">' . $v['interest_cost'] . '</span>';
            }
            if (!judgeNum($v['operator'])) {
                //操作人员数量不合法
                $assets[$k]['operator'] = '<span class="rquireCoin">' . $v['operator'] . '</span>';
            }
            if (!judgeNum($v['work_day'])) {
                //工作天数不合法
                $assets[$k]['work_day'] = '<span class="rquireCoin">' . $v['work_day'] . '</span>';
            }
            if (!judgeNum($v['positive_rate'])) {
                //诊疗阳性次数不合法
                $assets[$k]['positive_rate'] = '<span class="rquireCoin">' . $v['positive_rate'] . '</span>';
            }
            $assets[$k]['operation'] = $this->returnListLink('删除', $this->full_open_url($this->MODULE, $this->CONTROLLER) . 'batchAddBenefit', 'delTmpBenefit', C('BTN_CURRENCY') . ' layui-btn-danger delTmpBenefit');
        }
        $result['limit'] = (int)$limit;
        $result['offset'] = $offset;
        $result['total'] = (int)$total;
        $result['rows'] = $assets;
        $result['code'] = 200;
        //var_dump($assets);exit;
        return $result;
    }

    //删除明细
    public function delTempData()
    {
        $tempid = trim(I('POST.tempid'), ',');
        $tempArr = explode(',', $tempid);
        $res = $this->deleteData('assets_benefit_upload_temp', array('tempid' => array('in', $tempArr)));
        if ($res) {
            return array('status' => 1, 'msg' => '删除成功！');
        } else {
            return array('status' => -1, 'msg' => '删除失败！');
        }
    }

    //上传文件
    public function uploadData()
    {
        if (empty($_FILES)) {
            return array('status' => -1, 'msg' => '请上传文件');
        }
        $uploadConfig = array(
            'maxSize' => 3145728,
            'rootPath' => './Public/',
            'savePath' => 'uploads/',
            'saveName' => array('uniqid', ''),
            'exts' => array('xlsx', 'xls', 'xlsm'),
            'autoSub' => true,
            'subName' => array('date', 'Ymd'),
        );
        $upload = new \Think\Upload($uploadConfig);
        $info = $upload->upload();
        if (!$info) {
            return array('status' => -1, 'msg' => '导入数据出错');
        }
        vendor("PHPExcel.PHPExcel");
        $filePath = $upload->rootPath . $info['file']['savepath'] . $info['file']['savename'];
        if (empty($filePath) or !file_exists($filePath)) {
            die('file not exists');
        }
        $PHPReader = new \PHPExcel_Reader_Excel2007();        //建立reader对象
        if (!$PHPReader->canRead($filePath)) {
            $PHPReader = new \PHPExcel_Reader_Excel5();
            if (!$PHPReader->canRead($filePath)) {
                return array('status' => -1, 'msg' => '文件格式错误');
            }
        }
        $excelDate = new \PHPExcel_Shared_Date();
        $PHPExcel = $PHPReader->load($filePath);        //建立excel对象
        $currentSheet = $PHPExcel->getSheet(0);        //**读取excel文件中的指定工作表*/
        $allColumn = $currentSheet->getHighestColumn();        //**取得最大的列号*/
        ++$allColumn;
        $allRow = $currentSheet->getHighestRow();        //**取得一共有多少行*/
        $data = array();
        $cellname = array(
            'A' => 'entryDate',
            'B' => 'assnum',
            'C' => 'assets',
            'D' => 'model',
            'E' => 'department',
            'F' => 'income',
            'G' => 'depreciation_cost',
            'H' => 'material_cost',
            'I' => 'maintenance_cost',
            'J' => 'management_cost',
            'K' => 'operator',
            'L' => 'comprehensive_cost',
            'M' => 'interest_cost',
            'N' => 'work_day',
            'O' => 'work_number',
            'P' => 'positive_rate'
        );
        //需要进行日期处理的保存在一个数组

        //设备类型转换
        for ($rowIndex = 2; $rowIndex <= $allRow; $rowIndex++) {        //循环读取每个单元格的内容。注意行从1开始，列从A开始
            for ($colIndex = 'A'; $colIndex != $allColumn; $colIndex++) {
                $addr = $colIndex . $rowIndex;
                $cell = $currentSheet->getCell($addr)->getValue();
                if ($cell instanceof \PHPExcel_RichText) { //富文本转换字符串
                    $cell = $cell->__toString();
                }
                if ($cellname[$colIndex] == 'assets') {
                    if (!$cell) {
                        continue;
                    }
                }
                $data[$rowIndex - 2][$cellname[$colIndex]] = trim($cell) ? trim($cell) : '';
            }
        }
        if (!$data) {
            return array('status' => -1, 'msg' => '导入数据失败');
        }
        $assnums = [];
        foreach ($data as $v){
            if(!in_array($v['assnum'],$assnums)){
                $assnums[] = $v['assnum'];
            }
        }
        //获取设备临时表数据
        foreach ($assnums as $v){
            $ls_data = $this->DB_get_all('assets_benefit_upload_temp','assnum,entryDate,is_save',['assnum'=>$v],'','entryDate asc');
            if(!$ls_data){
                continue;
            }
            foreach ($data as $dk=>$dv){
                foreach ($ls_data as $lk=>$lv){
                    if($dv['assnum'] == $lv['assnum'] && $dv['entryDate'] == $lv['entryDate']){
                        //临时表已存在该条数据
                        unset($data[$dk]);
                    }
                }
            }
        }
        foreach ($data as $k => $v) {
            if (($v['income'] + $v['work_number'] + $v['depreciation_cost'] + $v['material_cost'] + $v['maintenance_cost'] + $v['comprehensive_cost'] + $v['interest_cost'] + $v['operator'] + $v['work_day'] + $v['positive_rate']) == 0) {
                //将空数据清空的数据删除
                unset($data[$k]);
            }
        }
        if (!$data) {
            //上传的文件数据和临时表中已存在数据重复
            return array('status' => -1, 'msg' => '该文件数据已上传过，请勿重复上传！');
        }

        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        $assetsWhere['hospital_id'] = ['EQ', session('current_hospitalid')];
        $assetsWhere['assnum'] = ['IN', $assnums];
        $assetsData = $this->DB_get_all('assets_info', 'assets,departid,assnum,model', $assetsWhere);
        //获取准备录入的设备合法信息（筛选出系统存在的设备）
        $assets = [];
        foreach ($assetsData as &$value) {
            $assets[$value['assnum']]['assets'] = $value['assets'];
            $assets[$value['assnum']]['model'] = $value['model'];
            $assets[$value['assnum']]['departid'] = $value['departid'];
            $assets[$value['assnum']]['department'] = $departname[$value['departid']]['department'];
        }
        //保存数据到临时表
        $insertData = array();
        $num = 0;
        foreach ($data as $k => $v) {
            if ($num < $this->len) {
                //$this->len条存一次数据到数据库
                if ($assets[$v['assnum']]) {
                    //如果编号存在就记录，避免记录错误的数据
                    $tempid = getRandomId();
                    $insertData[$num]['tempid'] = $tempid;
                    $insertData[$num]['adduser'] = session('username');
                    $insertData[$num]['adddate'] = time();
                    $insertData[$num]['is_save'] = 0;
                    foreach ($v as $k1 => $v1) {
                        $insertData[$num][$k1] = $v1;
                    }
                    //避免用户不小心修改设备信息，重新在设备表获取
                    $insertData[$num]['assets'] = $assets[$v['assnum']]['assets'];
                    $insertData[$num]['model'] = $assets[$v['assnum']]['model'];
                    $insertData[$num]['departid'] = $assets[$v['assnum']]['departid'];
                    $insertData[$num]['department'] = $assets[$v['assnum']]['department'];
                    $num++;
                }
            }
            if ($num == $this->len) {
                //插入数据
                $this->insertDataALL('assets_benefit_upload_temp', $insertData);
                //重置数据
                $num = 0;
                $insertData = array();
            }
        }
        if ($insertData) {
            $this->insertDataALL('assets_benefit_upload_temp', $insertData);
        }
        return array('status' => 1, 'msg' => '上传数据成功，请核对后再保存！');
    }

    //修改临时数据
    public function updateTempData()
    {
        $tempid = I('POST.tempid');
        $cloum = I('POST.cloum');
        $value = I('POST.value');
        switch ($cloum) {
            case 'work_number':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '工作天数不合法！');
                }
                break;
            case 'depreciation_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '折旧费用不合法！');
                }
                break;
            case 'material_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '材料费用不合法！');
                }
                break;
            case 'maintenance_cost':
                if ($value && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '维保费用不合法！');
                }
                break;
            case 'management_cost':
                if ($value && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '管理费用不合法！');
                }
                break;
            case 'comprehensive_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '综合费用不合法！');
                }
                break;
            case 'interest_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '利息支出不合法！');
                }
                break;
            case 'work_day':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '工作天数不合法！');
                }
                break;
            case 'operator':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '操作人员数量不合法！');
                }
                break;
            case 'positive_rate':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '诊疗阳性次数不合法！');
                }
                break;
        }
        $data[$cloum] = $value;
        $data['edituser'] = session('username');
        $data['editdate'] = time();
        $res = $this->updateData('assets_benefit_upload_temp', $data, array('tempid' => $tempid));
        if ($res) {
            return array('status' => 1, 'msg' => '修改成功！');
        } else {
            return array('status' => -1, 'msg' => '修改失败！');
        }
    }

    //保存数据
    public function batchAddBenefit()
    {
        $tempid = trim(I('POST.tempid'), ',');
        $tempArr = explode(',', $tempid);
        $num = 0;
        $saveTempidArr = array();
        foreach ($tempArr as $k => $v) {
            //按每次最多不超过$this->len条的数据获取临时表数据进行保存操作
            if ($num < $this->len) {
                $saveTempidArr[] = $v;
                $num++;
            }
            if ($num == $this->len) {
                //进行一次设备入库操作
                $this->benefitStorage($saveTempidArr);
                //重置
                $num = 0;
                $saveTempidArr = array();
            }
        }
        if ($saveTempidArr) {
            $this->benefitStorage($saveTempidArr);
        }
        return array('status' => 1, 'msg' => '保存数据成功！');
    }

    //修改临时数据
    public function updateBenefitData()
    {
        $assnum = I('POST.assnum');
        $cloum = I('POST.cloum');
        $value = I('POST.value');
        switch ($cloum) {
            case 'work_number':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '工作天数不合法！');
                }
                break;
            case 'depreciation_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '折旧费用不合法！');
                }
                break;
            case 'material_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '材料费用不合法！');
                }
                break;
            case 'maintenance_cost':
                if ($value && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '维保费用不合法！');
                }
                break;
            case 'management_cost':
                if ($value && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '管理费用不合法！');
                }
                break;
            case 'comprehensive_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '综合费用不合法！');
                }
                break;
            case 'interest_cost':
                if ($value < 0 && !checkPrice($value)) {
                    return array('status' => -1, 'msg' => '利息支出不合法！');
                }
                break;
            case 'operator':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '操作人员数量不合法！');
                }
                break;
            case 'work_day':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '工作天数不合法！');
                }
                break;
            case 'positive_rate':
                if (!judgeNum($value)) {
                    return array('status' => -1, 'msg' => '诊疗阳性次数不合法！');
                }
                break;
        }

        if ($assnum) {
            $entryDate = I('POST.entryDate');
            $assData = $this->DB_get_one('assets_info', 'departid', array('assnum' => $assnum));
            if (!$assData) {
                return array('status' => -1, 'msg' => '非法参数！');
            }
            $data['assnum'] = $assnum;
            $data['departid'] = $assData['departid'];
            $data['entryDate'] = $entryDate;
            $data[$cloum] = $value;
            $data['adduser'] = session('username');
            $data['adddate'] = time();
            $res = $benefitid = $this->insertData('assets_benefit', $data);
        } else {
            $benefitid = I('POST.benefitid');
            $data[$cloum] = $value;
            $data['edituser'] = session('username');
            $data['editdate'] = time();
            $res = $this->updateData('assets_benefit', $data, array('benefitid' => $benefitid));
        }
        if ($res) {
            $fileds = 'benefitid,income,work_number,depreciation_cost,material_cost,maintenance_cost,management_cost,comprehensive_cost,
            interest_cost,operator,work_day,positive_rate';
            $result = $this->DB_get_one('assets_benefit', $fileds, array('benefitid' => $benefitid));
            //录入成功的 计算结余
            $result['balance'] = $result['income'] - $result['depreciation_cost'] - $result['material_cost'] - $result['maintenance_cost'] - $result['management_cost'] - $result['comprehensive_cost'] - $result['interest_cost'];
            $result['surplus_rate'] = $result['income'] > 0 ? number_format($result['balance'] / $result['income'], 4, '.', '') * 100 : 0;
            return array('status' => 1, 'msg' => '修改成功！', 'result' => $result);
        } else {
            return array('status' => -1, 'msg' => '修改失败！');
        }
    }

    //导出模板
    public function exploreBenefitModel()
    {
        $startDate = I('GET.startDate');
        $endDate = I('GET.endDate');
        $department = I('GET.department');
        //导出模板
        $xlsName = "Benefit";
        //设备基础信息
        $fileds = 'assets,catid,assnum,model,departid';
        if ($department) {
            $dataWhere['departid'] = array('in', $department);
        } else {
            $dataWhere['departid'] = array('in', session('departid'));
        }
        $dataWhere['is_delete'] = array('EQ', C('NO_STATUS'));
        $dataWhere['is_benefit'] = array('EQ', C('YES_STATUS'));
        $dataWhere['hospital_id'] = array('EQ', session('current_hospitalid'));
        $data = $this->DB_get_all('assets_info', $fileds, $dataWhere, '', 'departid asc');
        if (!$data) {
            exit('没找到有需要进行效益分析的设备！只有设置了设备类型为“效益分析”的设备才会进行数据分析');
        }
        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        foreach ($data as &$Value) {
            $Value['department'] = $departname[$Value['departid']]['department'];
        }
        //用比较小的作为开始月份
        if ($startDate > $endDate) {
            $temporary = $startDate;
            $startDate = $endDate;
            $endDate = $temporary;
        }
        $where['entryDate'][] = array('EGT', $startDate);
        $where['entryDate'][] = array('ELT', $endDate);
        //收支明细
        $BenefitData = $this->DB_get_all('assets_benefit', '', $where);
        $BenefitRes = [];
        foreach ($BenefitData as &$BenefitValue) {
            //将已存在的明细记录组合成数组
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']] = true;
        }
        //计算月份差
        $monthNum = $this->getMonthNum($startDate, $endDate);
        $asArr = [];
        $key = 0;
        for ($i = 0; $i < $monthNum; $i++) {
            foreach ($data as &$value) {
                if (!$BenefitRes[$value['assnum'] . $startDate]) {
                    $asArr[$key]['entryDate'] = $startDate;
                    $asArr[$key]['assnum'] = $value['assnum'];
                    $asArr[$key]['assets'] = $value['assets'];
                    $asArr[$key]['model'] = $value['model'];
                    $asArr[$key]['department'] = $value['department'];
                    $asArr[$key]['income'] = '';
                    $asArr[$key]['work_number'] = '';
                    $asArr[$key]['depreciation_cost'] = '';
                    $asArr[$key]['material_cost'] = '';
                    $asArr[$key]['maintenance_cost'] = '';
                    $asArr[$key]['management_cost'] = '';
                    $asArr[$key]['operator'] = '';
                    $asArr[$key]['comprehensive_cost'] = '';
                    $asArr[$key]['interest_cost'] = '';
                    $asArr[$key]['work_day'] = '';
                    $asArr[$key]['positive_rate'] = '';
                    $key++;
                }
                //匹配数组数据

            }
            $startDate = date("Y-m", strtotime($startDate . "+1 month"));
        }
        $xlsCell = array(
            'entryDate' => '录入月份',
            'assnum' => '设备编号',
            'assets' => '设备名称',
            'model' => '设备型号',
            'department' => '所属科室',
            'income' => '月收入',
            'depreciation_cost' => '折旧费',
            'material_cost' => '材料费',
            'maintenance_cost' => '维保费',
            'management_cost' => '管理费',
            'operator' => '操作人员数量',
            'comprehensive_cost' => '综合费',
            'interest_cost' => '利息支出',
            'work_day' => '工作天数',
            'work_number' => '诊疗次数',
            'positive_rate' => '诊疗阳性次数',
        );
        //单元格宽度设置
        $width = array(
            '录入月份' => '15',
            '设备编号' => '30',//字符数长度
            '设备名称' => '20',
            '设备型号' => '20',
            '所属科室' => '20',
            '月收入' => '15',

            '折旧费' => '15',
            '材料费' => '15',
            '维保费维保费' => '15',
            '管理费' => '15',
            '操作人员数量' => '20',
            '综合费' => '15',
            '利息支出' => '15',
            '工作天数' => '15',
            '诊疗次数' => '15',
            '诊疗阳性次数' => '25',

        );
        //单元格颜色设置（例如必填行单元格字体颜色为红色）
        $color = array(
            '录入月份' => 'FF0000',//颜色代码
            '设备编号' => 'FF0000',
            '设备名称' => 'FF0000',
            '设备型号' => 'FF0000',
            '所属科室' => 'FF0000',
        );
        exportBenefitTemplate('设备明细导入模板', $xlsName, $xlsCell, $asArr, $width, $color);
    }

    //导出设备收支明细数据
    public function exportBenefit()
    {
        $departid = session('departid');
        $assetsDep = I('POST.assetsDep');
        $Date = I('POST.assetsBenefitListDate');
        $assetsName = I('POST.assetsName');
        $hospital_id = I('POST.hospital_id');
        $where['entryDate'] = array('EQ', $Date);
        $where['departid'][] = array('IN', $departid);
        if ($assetsName) {
            $where['assets'] = array('LIKE', '%' . $assetsName . '%');
        }
        $departname = [];
        include APP_PATH . "Common/cache/department.cache.php";
        //部门搜索
        if ($assetsDep != '') {
            foreach ($departname as &$one) {
                if (array_keys($one, $assetsDep)) {
                    $assetsDep = array_keys($departname, $one)[0];
                }
            }
            $where['departid'][] = array('EQ', $assetsDep);
        }
        if ($hospital_id) {
            $where['hospital_id'] = array('EQ', $hospital_id);
        } else {
            $where['hospital_id'] = array('EQ', session('current_hospitalid'));
        }
        $fileds = 'assets,catid,assnum,model,departid';
        $asArr = $this->DB_get_all('assets_info', $fileds, $where, '', 'assid desc');
        if (!$asArr) {
            die(json_encode(array('status' => -1, 'msg' => '暂无数据')));
        }
        $catname = [];
        include APP_PATH . "Common/cache/category.cache.php";
        $assnumArr = [];
        foreach ($asArr as &$one) {
            $assnumArr [] = $one['assnum'];
            $one['category'] = $catname[$one['catid']]['category'];
        }
        //获取对应明细数据
        $fileds = 'assnum,departid,entryDate,income,work_number,depreciation_cost,material_cost,maintenance_cost,
        management_cost,interest_cost,comprehensive_cost,operator,work_day,positive_rate';
        $BenefitWhere['assnum'] = array('IN', $assnumArr);
        $BenefitWhere['entryDate'] = array('EQ', $Date);
        $BenefitData = $this->DB_get_all('assets_benefit', $fileds, $BenefitWhere);
        $BenefitRes = [];
        foreach ($BenefitData as &$BenefitValue) {
            //生成用对用唯一值做键值得数组
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['income'] = $BenefitValue['income'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['departid'] = $BenefitValue['departid'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['entryDate'] = $BenefitValue['entryDate'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['work_number'] = $BenefitValue['work_number'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['depreciation_cost'] = $BenefitValue['depreciation_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['material_cost'] = $BenefitValue['material_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['maintenance_cost'] = $BenefitValue['maintenance_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['management_cost'] = $BenefitValue['management_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['operator'] = $BenefitValue['operator'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['comprehensive_cost'] = $BenefitValue['comprehensive_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['interest_cost'] = $BenefitValue['interest_cost'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['work_day'] = $BenefitValue['work_day'];
            $BenefitRes[$BenefitValue['assnum'] . $BenefitValue['entryDate']]['positive_rate'] = $BenefitValue['positive_rate'];
        }
        foreach ($asArr as &$one) {
            $one['income'] = $BenefitRes[$one['assnum'] . $Date]['income'];
            $one['entryDate'] = $Date;
            $one['work_number'] = $BenefitRes[$one['assnum'] . $Date]['work_number'];
            $one['depreciation_cost'] = $BenefitRes[$one['assnum'] . $Date]['depreciation_cost'];
            $one['material_cost'] = $BenefitRes[$one['assnum'] . $Date]['material_cost'];
            $one['maintenance_cost'] = $BenefitRes[$one['assnum'] . $Date]['maintenance_cost'];
            $one['management_cost'] = $BenefitRes[$one['assnum'] . $Date]['management_cost'];
            $one['operator'] = $BenefitRes[$one['assnum'] . $Date]['operator'];
            $one['comprehensive_cost'] = $BenefitRes[$one['assnum'] . $Date]['comprehensive_cost'];
            $one['interest_cost'] = $BenefitRes[$one['assnum'] . $Date]['interest_cost'];
            $one['work_day'] = $BenefitRes[$one['assnum'] . $Date]['work_day'];
            if ($BenefitRes[$one['assnum'] . $Date]['departid']) {
                $one['positive_rate'] = $BenefitRes[$one['assnum'] . $Date]['positive_rate'];
                $one['department'] = $departname[$BenefitRes[$one['assnum'] . $Date]['departid']]['department'];
                //录入成功的 计算结余
                $one['balance'] = $one['income'] - $one['depreciation_cost'] - $one['material_cost'] - $one['maintenance_cost'] - $one['management_cost'] - $one['comprehensive_cost'] - $one['interest_cost'];
                if ($one['income'] > 0) {
                    $one['surplus_rate'] = $one['income'] > 0 ? (number_format($one['balance'] / $one['income'], 4, '.', '') * 100) . '%' : '0%';
                } else {
                    $one['surplus_rate'] = '';
                }
            } else {
                $one['department'] = $departname[$one['departid']]['department'];
            }
        }
        $showName = array(
            'entryDate' => '录入月份',
            'assnum' => '设备编号',
            'assets' => '设备名称',
            'model' => '设备型号',
            'department' => '所属科室',
            'income' => '月收入',
            'depreciation_cost' => '折旧费',
            'material_cost' => '材料费',
            'maintenance_cost' => '维保费维保费',
            'management_cost' => '管理费',
            'operator' => '操作人员数量',
            'comprehensive_cost' => '综合费',
            'interest_cost' => '利息支出',
            'work_day' => '工作天数',
            'work_number' => '诊疗次数',
            'positive_rate' => '诊疗阳性次数',
            'balance' => '结余',
            'surplus_rate' => '结余率(%)'
        );
        exportAssets(array('设备收支明细'), $Date . '设备收支明细', $showName, $asArr);
    }


    /**
     * Notes: 计算月份差
     * @param $date1 string 开始月份
     * @param $date2 string 结束月份
     * @param $tags string 日期分隔符
     * @return array
     */
    function getMonthNum($date1, $date2, $tags = '-')
    {
        $date1 = explode($tags, $date1);
        $date2 = explode($tags, $date2);
        return abs($date1[0] - $date2[0]) * 12 + abs($date1[1] - $date2[1]) + 1;
    }

    /**
     * Notes: 明细量入库方法
     * @param $saveTempidArr array 要保存的临时表设备ID
     * @return array
     */
    public function benefitStorage($saveTempidArr)
    {
        $where['tempid'] = array('IN', $saveTempidArr);
        $where['is_save'] = array('EQ', C('NO_STATUS'));
        $upload_temp_data = $this->DB_get_all('assets_benefit_upload_temp', '', $where);
        if (!$upload_temp_data) {
            die(json_encode(array('status' => -999, 'msg' => '数据异常')));
        }
        $newData = [];
        $assnumarr = [];
        $key = 0;
        foreach ($upload_temp_data as &$one) {
            if ($one['income'] < 0 && !checkPrice($one['income'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '月收入不合法')));
            }
            if (!judgeNum($one['work_number'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '工作天数不合法')));
            }
            if ($one['depreciation_cost'] < 0 && !checkPrice($one['depreciation_cost'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '折旧费用不合法')));
            }
            if ($one['material_cost'] < 0 && !checkPrice($one['material_cost'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '材料费用不合法')));
            }
            if ($one['maintenance_cost'] < 0 && !checkPrice($one['maintenance_cost'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '维保费用不合法')));
            }
            if ($one['management_cost'] < 0 && !checkPrice($one['management_cost'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '管理费用不合法')));
            }
            if ($one['comprehensive_cost'] < 0 && !checkPrice($one['comprehensive_cost'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '综合费用不合法')));
            }
            if ($one['interest_cost'] < 0 && !checkPrice($one['interest_cost'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '利息支出不合法')));
            }
            if (!judgeNum($one['operator'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '操作人员数量不合法')));
            }
            if (!judgeNum($one['work_day'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '工作天数不合法')));
            }
            if (!judgeNum($one['positive_rate'])) {
                die(json_encode(array('status' => -999, 'msg' => $one . ['assets'] . '诊疗阳性次数不合法')));
            }
            $assnumarr[] = $one['assnum'];

        }
        //避免直接操作接口出现的科室错乱数据
        $assData = $this->DB_get_all('assets_info', 'assnum,departid', array('assnum' => array('IN', $assnumarr)));
        $formatAssets = [];
        foreach ($assData as &$one) {
            $formatAssets[$one['assnum']]['departid'] = $one['departid'];
        }
        foreach ($upload_temp_data as &$one) {
            //避免重复数据
            $exists = $this->DB_get_one('assets_benefit','benefitid',['assnum'=>$one['assnum'],'entryDate'=>$one['entryDate']]);
            if(!$exists){
                //不存在
                if ($formatAssets[$one['assnum']]) {
                    $newData[$key]['assnum'] = $one['assnum'];
                    $newData[$key]['departid'] = $formatAssets[$one['assnum']]['departid'];
                    $newData[$key]['entryDate'] = $one['entryDate'];
                    $newData[$key]['income'] = $one['income'];
                    $newData[$key]['work_number'] = $one['work_number'];
                    $newData[$key]['depreciation_cost'] = $one['depreciation_cost'];
                    $newData[$key]['material_cost'] = $one['material_cost'];
                    $newData[$key]['maintenance_cost'] = $one['maintenance_cost'];
                    $newData[$key]['management_cost'] = $one['management_cost'];
                    $newData[$key]['comprehensive_cost'] = $one['comprehensive_cost'];
                    $newData[$key]['interest_cost'] = $one['interest_cost'];
                    $newData[$key]['operator'] = $one['operator'];
                    $newData[$key]['work_day'] = $one['work_day'];
                    $newData[$key]['positive_rate'] = $one['positive_rate'];
                    $newData[$key]['adddate'] = time();
                    $newData[$key]['adduser'] = session('username');
                    $key++;
                }
            }
        }
        if(!$newData){
            die(json_encode(array('status' => -999, 'msg' => '没有数据被录入')));
        }
        $add = $this->insertDataALL('assets_benefit', $newData);
        if ($add) {
            $saveTemp['is_save'] = C('YES_STATUS');
            $this->updateData('assets_benefit_upload_temp', $saveTemp, $where);
        } else {
            die(json_encode(array('status' => -999, 'msg' => '批量录入失败')));
        }
    }
}