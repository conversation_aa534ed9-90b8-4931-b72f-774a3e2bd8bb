<?php
return [
    // 默认模块
    'DEFAULT_MODULE' => 'App',
    // 默认控制器名称
    'DEFAULT_CONTROLLER' => 'Login/Index',
    // 默认操作名称
    'DEFAULT_ACTION' => 'index',
    // 异常页面的模板文件 错误显示信息,非调试模式有效
    'ERROR_MESSAGE' => '页面错误',
    // 异常页面的模板文件
    'TMPL_EXCEPTION_FILE' => './404_mobile.tpl',
    'MD5_KEY' => 'sdmlkiovidkl',
    //配置报表医院名称
    'HOSPITAL' => '配置医院名称',
    //强制分类编码开头的字符值，不需要的话请置空
    'CATE_PREFIX_NUM' => '68',
    //强制部门编码开头的字符值，不需要的话请置空
    'DEPART_PREFIX_NUM' => '',
    // 用来开发定制二维码识别
    'QRCODE_REGEX' => env('qrcode_regex') ? [
        env('qrcode_regex'),
    ] : [

    ],
    //微信appid
    'WX_APPID' => env('wx_appid'),
    //微信secret
    'WX_SECRET' => env('wx_secret'),
];
